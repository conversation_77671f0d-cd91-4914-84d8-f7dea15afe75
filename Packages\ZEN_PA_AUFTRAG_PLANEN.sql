CREATE OR REPLACE PACKAGE BODY ZEN.PA_AUFTRAG_PLANEN AS

--$Archive: /storelogix/LVS/sql/Packages/PA_AUFTRAG_PLANEN.pck $
--$Revision: 129 $
--$Modtime: 24.12.14 11:37 $
--$Author: Sgraf $

lTraceLevel       PLS_INTEGER := BASE_TRACING.GETTRACELEVEL;

--<PERSON><PERSON><PERSON> alle Auftragsposition mit MENGE_SOLL > 0
CURSOR c_auftrag_pos   (pREF IN AUFTRAG.REF%TYPE) IS
  select
      ap.*
    FROM
      AUFTRAG a, AUFTRAG_POS ap
    WHERE
      a.REF=ap.REF_AUF_KOPF and
      ((ap.MENGE_BESTELLT is not null and nvl (ap.MENGE_SOLL, 0) > 0) or (ap.MENGE_BESTELLT is null and nvl (ap.GEWICHT_SOLL,0) > 0)) and
      ap.REF_AUF_KOPF=pRef and
      ap.REF_AR not in (select REF_AR from ARTIKEL_REL_KOMM_GRUPPE where REF_KOMM_GRP in (select REF from ARTIKEL_KOMM_GRUPPE where
       STATUS='AKT' and REF_MAND=a.REF_MAND and REF_LAGER=a.REF_LAGER)) ORDER BY POS_NR ASC;

CURSOR c_bestand_frei  (pRefLager   in LAGER_BESTAND_SUMME.REF_LAGER%TYPE,
                        pRefArtikel IN LAGER_BESTAND_SUMME.REF_AR%TYPE,
                        pRefEinheit in ARTIKEL_EINHEIT.REF_EINHEIT%TYPE,
                        pARVariante IN LAGER_BESTAND_SUMME.AR_VARIANTE%TYPE,
                        pMenge      in AUFTRAG_POS.MENGE_SOLL%TYPE,
                        pStriktFifo in char
                        ) is
  select
      b.*
    from
      VQ_LAGER_BESTAND_SUMME_PLAN b
      inner join ARTIKEL_EINHEIT ae on (ae.REF=b.REF_AR_EINHEIT)
  where
    b.REF_LAGER=pRefLager and
    b.REF_AR=pRefArtikel and
    (select REF_EINHEIT from ARTIKEL_EINHEIT where REF=b.REF_AR_EINHEIT)=pRefEinheit and
    ((pARVariante is null and b.AR_VARIANTE is NULL) or pARVariante=AR_VARIANTE)
    --and (NVL (b.MENGE_FREI,0) - nvl (b.MENGE_RES, 0) - nvl (b.MENGE_WE, 0) - nvl (b.MENGE_VOR_RES, 0)) > 0
    and (NVL (b.MENGE_FREI,0) - nvl (b.MENGE_RES, 0) - nvl (b.MENGE_WE, 0)) > 0
  ORDER BY
    case
      when (pStriktFifo = '1') then
        0
      when ((ae.PAL_FAKTOR is not null) and (pMenge >= ae.PAL_FAKTOR) and (nvl (b.MENGE_RES, 0) = 0) and (nvl (b.MENGE_FREI,0) >= ae.PAL_FAKTOR)) then
        1
      else
        9
    end asc,
    b.MHD ASC,
    b.CHARGE asc,
    b.MENGE_FREI ASC;

CURSOR c_colli_bestand_frei  (pRefLager   in LAGER_BESTAND_SUMME.REF_LAGER%TYPE,
                              pRefArtikel IN LAGER_BESTAND_SUMME.REF_AR%TYPE,
                              pRefEinheit in ARTIKEL_EINHEIT.REF_EINHEIT%TYPE,
                              pARVariante IN LAGER_BESTAND_SUMME.AR_VARIANTE%TYPE) is
  select
      b.*
    from
      VQ_LAGER_BESTAND_SUMME_PLAN b
  where
    b.REF_LAGER=pRefLager and
    b.REF_AR=pRefArtikel and
    (select REF_EINHEIT from ARTIKEL_EINHEIT where REF=b.REF_AR_EINHEIT)=pRefEinheit and
    ((pARVariante is null and b.AR_VARIANTE is NULL) or pARVariante=AR_VARIANTE)
  ORDER BY
    b.CHARGE asc,
    b.MENGE_FREI ASC;

TYPE FehlerEntry IS RECORD
(
  RefKopf      AUFTRAG.REF%TYPE,
  RefPos       AUFTRAG_POS.REF%TYPE,
  RefLTPos     AUFTRAG_POS_LT.REF%TYPE,
  RefArEinheit ARTIKEL_EINHEIT.REF%TYPE,
  PosNr        INTEGER,
  Status       VARCHAR2 (16),
  Text         VARCHAR2 (1024),
  SollMenge    PLS_INTEGER,
  FehlMenge    PLS_INTEGER,
  Gewicht      PLS_INTEGER
);

TYPE FehlerTabelle    IS TABLE OF FehlerEntry    INDEX BY PLS_INTEGER;

FehlerTab FehlerTabelle;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BooleanToStr (pFlag in boolean) return string is
begin
  return case
           when pFlag is null then
             'null'
           when pFlag = true then
             'true'
           when pFlag = false then
             'false'
           else
             '?'
         end;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function GetKommOpt (pAufRow in AUFTRAG%rowtype) return varchar2 is
  v_l_komm_opt  varchar2 (32);
  v_w_komm_opt  varchar2 (32);
  v_komm_opt    varchar2 (32);

  aktlevel  PLS_INTEGER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('GetKommOpt');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pAufRow.REF   ='||pAufRow.REF);
  end if;

  select KOMM_OPT into v_l_komm_opt from LAGER where REF=pAufRow.REF_LAGER;

  begin
    select
        KOMM_OPTIONS into v_w_komm_opt
      from
        (select
            KOMM_OPTIONS
          from
            WARENEMPF_REL_KOMM
          where
            REF_WARENEMPF=pAufRow.REF_WARENEMPF and
            (
             (REF_LAGER is null and REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=pAufRow.REF_LAGER)) or
             REF_LAGER=pAufRow.REF_LAGER
            )
          order by
            REF_LAGER nulls last
        )
      where ROWNUM=1;
    exception
      when no_data_found then
        v_w_komm_opt := null;
      when others then
        raise;
  end;

  if (lTraceLevel >= 3) then
    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_w_komm_opt ='||v_w_komm_opt);
  end if;

  v_komm_opt := '';

  if (substr (v_l_komm_opt, PA_LAGER.KOMM_OPT_PLAN_VOLLPAL, 1) = '1')        then v_komm_opt := v_komm_opt || '1'; else v_komm_opt := v_komm_opt || '0'; end if;
  if (substr (v_l_komm_opt, PA_LAGER.KOMM_OPT_PLAN_NVE_PAL_FAKTOR, 1) = '1') then v_komm_opt := v_komm_opt || '1'; else v_komm_opt := v_komm_opt || '0'; end if;

  if (substr (v_l_komm_opt, 3, 1) = '1') then v_komm_opt := v_komm_opt || '1'; else v_komm_opt := v_komm_opt || '0'; end if;
  if (substr (v_l_komm_opt, 4, 1) = '1') then v_komm_opt := v_komm_opt || '1'; else v_komm_opt := v_komm_opt || '0'; end if;

  if (substr (v_w_komm_opt, 1, 1) = '0') then
    v_komm_opt := v_komm_opt || '0';
  elsif (substr (v_l_komm_opt, PA_LAGER.KOMM_OPT_ITEM_COMPACTED, 1) = '1') then
    v_komm_opt := v_komm_opt || '1';
  else
    v_komm_opt := v_komm_opt || '0';
  end if;

  if (substr (v_l_komm_opt, PA_LAGER.KOMM_OPT_RES_BESTAND, 1) = '1') then v_komm_opt := v_komm_opt || '1'; else v_komm_opt := v_komm_opt || '0'; end if;

  v_komm_opt := v_komm_opt || '0';
  v_komm_opt := v_komm_opt || '0';

  BASE_TRACING.LEAVEPROC (v_komm_opt);

  return v_komm_opt;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function GetKommOpt (pRefAuf in AUFTRAG.REF%type) return varchar2 is
  vr_auf        AUFTRAG%rowtype;
  v_komm_opt    varchar2 (32);

  aktlevel  PLS_INTEGER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('GetKommOpt');

  BASE_TRACING.TraceOutput (2,'pRefAuf   ='||pRefAuf);

  select * into vr_auf from AUFTRAG where REF=pRefAuf;

  v_komm_opt := GetKommOpt (vr_auf);

  BASE_TRACING.LEAVEPROC (v_komm_opt);

  return v_komm_opt;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
procedure SetKommPlanLB (pRefMand in MANDANT.REF%TYPE, pRefLager in LAGER.REF%TYPE, pAuftragArt in varchar2) is
begin
  SetKommPlanLB (pRefMand, pRefLager, pAuftragArt, null);
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
procedure SetKommPlanLB (pRefMand in MANDANT.REF%TYPE, pRefLager in LAGER.REF%TYPE, pAuftragArt in varchar2, RefBatchCfg in BATCHLAUF_CONFIG.REF%type) is
  vr_mand   MANDANT%rowtype;
  vr_tkpl   TMP_KOMM_PLAN_LB%rowtype;
  v_count   PLS_INTEGER;
  aktlevel  PLS_INTEGER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('SetKommPlanLB');
  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pRefMand    ='||pRefMand);
    BASE_TRACING.TraceOutput (2,'pRefLager   ='||pRefLager);
    BASE_TRACING.TraceOutput (2,'pAuftragArt ='||pAuftragArt);
    BASE_TRACING.TraceOutput (2,'RefBatchCfg ='||RefBatchCfg);
  end if;

  select * into vr_mand from MANDANT where REF=pRefMand;

  --Erstmal keine Anschränkung beim Suchen nach Beständen
  delete from TMP_KOMM_PLAN_LB;

  v_count := 0;

  if (RefBatchCfg is not null) then
    for cr_lb in (select * from AUFTRAG_ART_REL_LB where STATUS='AKT' and REF_BATCHLAUF_CONFIG=RefBatchCfg) loop
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'> cr_lb : PRIO='||cr_lb.PRIO||', REF_LB='||cr_lb.REF_LB||', OPT_VOLL_PAL='||cr_lb.OPT_VOLL_PAL||', OPT_KOMM_VPE='||cr_lb.OPT_KOMM_VPE||', OPT_KOMM_ITEM='||cr_lb.OPT_KOMM_ITEM||', OPT_SELECT='||cr_lb.OPT_SELECT);
      end if;

      begin
        select * into vr_tkpl from TMP_KOMM_PLAN_LB where REF_LB=cr_lb.REF_LB and nvl (REF_LB_ZONE,-1)=nvl (cr_lb.REF_LB_ZONE,-1) and ((PRIO is null and cr_lb.PRIO is null) or (PRIO=cr_lb.PRIO));

        exception
          when no_data_found then
            vr_tkpl := null;
          when others then
            raise;
      end;

      --Der LB darf nur einmal vorhanden sein
      if (vr_tkpl.REF_LB is null) then
        v_count := v_count + 1;

        insert into TMP_KOMM_PLAN_LB
            (REF_AUFTRAG_ART_REL_LB, REF_LB, REF_LB_ZONE, OPT_NOT_CHECK_LP, OPT_VOLL_PAL, OPT_KOMM_VPE, OPT_KOMM_ITEM, OPT_SELECT, PRIO, MIN_VPE, MAX_VPE, MIN_BESTAND)
          values
            (cr_lb.REF, cr_lb.REF_LB, cr_lb.REF_LB_ZONE, cr_lb.OPT_NOT_CHECK_LP, cr_lb.OPT_VOLL_PAL, cr_lb.OPT_KOMM_VPE, cr_lb.OPT_KOMM_ITEM, cr_lb.OPT_SELECT, cr_lb.PRIO, cr_lb.MIN_VPE, cr_lb.MAX_VPE, cr_lb.MIN_BESTAND);
      end if;
    end loop;
  end if;

  if (v_count = 0) then
    for cr_lb in (select * from AUFTRAG_ART_REL_LB where STATUS='AKT' and (REF_MAND=pRefMand or (vr_mand.REF_MASTER_MAND is not null and REF_MAND=vr_mand.REF_MASTER_MAND)) and REF_LAGER=pRefLager and REF_BATCHLAUF_CONFIG is null and (AUFTRAG_ART is null or AUFTRAG_ART=pAuftragArt)) loop
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'> cr_lb : PRIO='||cr_lb.PRIO||', REF_LB='||cr_lb.REF_LB||', OPT_VOLL_PAL='||cr_lb.OPT_VOLL_PAL||', OPT_KOMM_VPE='||cr_lb.OPT_KOMM_VPE||', OPT_KOMM_ITEM='||cr_lb.OPT_KOMM_ITEM||', OPT_SELECT='||cr_lb.OPT_SELECT);
      end if;

      begin
        select * into vr_tkpl from TMP_KOMM_PLAN_LB where REF_LB=cr_lb.REF_LB and nvl (REF_LB_ZONE,-1)=nvl (cr_lb.REF_LB_ZONE,-1) and ((PRIO is null and cr_lb.PRIO is null) or (PRIO=cr_lb.PRIO));

        exception
          when no_data_found then
            vr_tkpl := null;
          when others then
            raise;
      end;

      --Der LB darf nur einmal vorhanden sein
      if (vr_tkpl.REF_LB is null) then
        v_count := v_count + 1;

        insert into TMP_KOMM_PLAN_LB
            (REF_AUFTRAG_ART_REL_LB, REF_LB, REF_LB_ZONE, OPT_NOT_CHECK_LP, OPT_VOLL_PAL, OPT_KOMM_VPE, OPT_KOMM_ITEM, OPT_SELECT, PRIO, MIN_VPE, MAX_VPE, MIN_BESTAND)
          values
            (cr_lb.REF, cr_lb.REF_LB, cr_lb.REF_LB_ZONE, cr_lb.OPT_NOT_CHECK_LP, cr_lb.OPT_VOLL_PAL, cr_lb.OPT_KOMM_VPE, cr_lb.OPT_KOMM_ITEM, cr_lb.OPT_SELECT, cr_lb.PRIO, cr_lb.MIN_VPE, cr_lb.MAX_VPE, cr_lb.MIN_BESTAND);
      end if;
    end loop;
  end if;

  BASE_TRACING.LeaveProcProc;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CreateFehlerliste (pStatus in varchar2, pErrorText in varchar2) return integer is
  aktlevel  PLS_INTEGER;

  v_ref_err      FEHLER_LISTE.REF%TYPE;
  v_ref_err_pack FEHLER_LISTE.REF%TYPE;
  v_ref_err_pos  FEHLER_POS.REF%TYPE;
  v_ref_pack     AUFTRAG_PACKLISTE.REF%TYPE;

  v_tab          PLS_INTEGER;

  res	           NUMBER;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('CreateFehlerliste');
  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pStatus   ='||pStatus);
    BASE_TRACING.TraceOutput (2,'pErrorText='||pErrorText);
  end if;

  v_tab := FehlerTab.FIRST;

  loop
    begin
      select REF into v_ref_err from FEHLER_LISTE where REF=(select REF_FEHLER from AUFTRAG where REF=FehlerTab (v_tab).RefKopf);

      exception
        when no_data_found then
          v_ref_err := null;

        when others then
          raise;
    end;

    if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ref_err='||v_ref_err); end if;

    if (v_ref_err is null) then
      insert into FEHLER_LISTE
          (REF_ID, FEHLER_STATUS, FEHLER_TEXT, ID_TYP)
        values
          (FehlerTab (v_tab).RefKopf, pStatus, pErrorText, 'AUF')
        returning REF into v_ref_err;

      update AUFTRAG set FEHLER_STATUS=pStatus, REF_FEHLER=v_ref_err where REF=FehlerTab (v_tab).RefKopf;

      select REF_PACKLISTE into v_ref_pack from AUFTRAG where REF=FehlerTab (v_tab).RefKopf;

      if (v_ref_pack is null) then
        v_ref_err_pack := null;
      else
        begin
          select REF into v_ref_err_pack from FEHLER_LISTE where REF=(select REF_FEHLER from AUFTRAG_PACKLISTE where REF=v_ref_pack);

          exception
            when no_data_found then
              v_ref_err_pack := null;

            when others then
              raise;
        end;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ref_err_pack='||v_ref_err_pack);
        end if;

        if (v_ref_err_pack is null) then
          insert into FEHLER_LISTE
              (REF_ID, FEHLER_STATUS, FEHLER_TEXT,ID_TYP)
            values
              (v_ref_pack, pStatus, pErrorText, 'PACK')
            returning REF into v_ref_err_pack;
        end if;

        update AUFTRAG_PACKLISTE set FEHLER_STATUS=pStatus, REF_FEHLER=v_ref_err_pack where REF=v_ref_pack;
      end if;
    end if;

    insert into FEHLER_POS
        (REF_LISTE, REF_ID, REF_POS, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
      values
        (v_ref_err, FehlerTab (v_tab).RefPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht)
      returning REF into v_ref_err_pos;

    update AUFTRAG_POS set REF_FEHLER_POS=v_ref_err_pos where REF=FehlerTab (v_tab).RefPos;

    if (v_ref_err_pack is not null) then
      insert into FEHLER_POS
          (REF_LISTE, REF_ID, REF_POS, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
        values
          (v_ref_err_pack, FehlerTab (v_tab).RefPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht);
    end if;

    v_tab := FehlerTab.NEXT (v_tab);

    EXIT WHEN v_tab IS NULL;
  end loop;

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BUILD_RES_PLANUNG (pRefBatCfg in BATCHLAUF_CONFIG.REF%TYPE, pRefSped in SPEDITIONEN.REF%type, pMaxAuf in integer, pRefPlan out TMP_RES_PLAN.REF%type) return INTEGER is
  aktlevel  PLS_INTEGER;

  vr_bat_cfg  BATCHLAUF_CONFIG%rowtype;
  vr_auf_adr  AUFTRAG_ADR%rowtype;
  vr_ar       ARTIKEL%rowtype;
  vr_ae       ARTIKEL_EINHEIT%rowtype;

  v_ref_loc   LOCATION.REF%type;
  v_menge_use AUFTRAG_POS.MENGE_SOLL%type;
  v_ok        boolean;
  v_bes       boolean;
  v_count     PLS_INTEGER;
  v_ref_res   TMP_RES_PLAN_BESTAND.REF%type;

  v_check     PLS_INTEGER;
  v_cur_count integer;
  v_auf_count integer;

  res         integer;

  procedure ResPlanBestand (prAufPos in AUFTRAG_POS%rowtype, pRefAR in ARTIKEL.REF%type, pRefEinheit in ARTIKEL_VPE.REF%type, pMenge in integer, oBesFlag out Boolean, oRefRes out TMP_RES_PLAN_BESTAND.REF%type) is
    vr_tmp_res  TMP_RES_PLAN_BESTAND%rowtype;
    v_sum_frei  LAGER_BESTAND.MENGE_FREI%type;
    v_sum_res   LAGER_BESTAND.MENGE_RES%type;
  begin
    oBesFlag := true;

    BASE_TRACING.TRACETIMESTAMP (2, '#'||$$plsql_line||': pMenge='||pMenge);

    begin
      select * into vr_tmp_res from TMP_RES_PLAN_BESTAND where REF_PLAN=pRefPlan and REF_AR=pRefAR and REF_EINHEIT=pRefEinheit;

      exception
        when no_data_found then
          vr_tmp_res := null;
        when others then
          raise;
    end;

    --BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||': vr_tmp_res.REF='||vr_tmp_res.REF);

    if (vr_tmp_res.REF is not null) then
      if (lTraceLevel >= 3) then
        BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||': pRefAR='||pRefAR||', pRefEinheit='||pRefEinheit||' = vr_tmp_res : REF='||vr_tmp_res.REF||', MENGE_FREI='||vr_tmp_res.MENGE_FREI||', MENGE_RES='||vr_tmp_res.MENGE_RES);
      end if;

      if ((nvl (vr_tmp_res.MENGE_FREI, 0) - nvl (vr_tmp_res.MENGE_RES, 0)) >= pMenge) then
        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 1, pMenge);
      else
        oBesFlag := false;

        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 0, pMenge);
      end if;
    else
      select
          sum (MENGE_FREI), sum (MENGE_RES) into v_sum_frei, v_sum_res
        from
          LAGER_BESTAND bes
          inner join ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)
          left outer join LAGER_LE le on (bes.REF_LE is not null and le.REF=bes.REF_LE)
          inner join  LAGER_LP lp on ((bes.REF_LP is not null and lp.REF=bes.REF_LP) or (bes.REF_LE is not null and lp.REF=le.REF_LP))
          inner join TMP_KOMM_PLAN_LP tmp on (tmp.REF_LP=lp.REF)
        where
          bes.STATUS='AKT' and
          --Der Bestand muss aus dem Lager kommen
          bes.REF_LAGER=vr_bat_cfg.REF_LAGER and
          --Artikel udn einhgiet müssen passen
          bes.REF_AR=pRefAR and ae.REF_EINHEIT=pRefEinheit and
          --Nicht in Inventur
          bes.REF_INV_POS is null and
          --LE nicht in Transport
          le.REF_TA is null;

      if (lTraceLevel >= 3) then
        BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||': pRefAR='||pRefAR||', pRefEinheit='||pRefEinheit||' : v_sum_frei='||v_sum_frei||', v_sum_res='||v_sum_res);
      end if;

      insert into TMP_RES_PLAN_BESTAND
          (REF_PLAN, REF_AR, REF_EINHEIT, MENGE_FREI, MENGE_RES)
        values
          (pRefPlan, pRefAR, pRefEinheit, nvl (v_sum_frei, 0), v_sum_res)
        returning REF into vr_tmp_res.REF;

      if ((nvl (v_sum_frei, 0) - nvl (v_sum_res, 0)) >= pMenge) then
        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 1, pMenge);
      else
        oBesFlag := false;

        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 0, pMenge);
      end if;
    end if;

    oRefRes := vr_tmp_res.REF;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TRACETIMESTAMP (5, '#'||$$plsql_line||' oRefRes='||oRefRes||' oBesFlag='||case when oBesFlag is null then 'null' when oBesFlag then 'true' else 'false' end);
    end if;
  end;

  procedure ResPlanBestand (prAufPos in AUFTRAG_POS%rowtype, pRefAE in ARTIKEL_EINHEIT.REF%type, pMenge in integer, oBesFlag out Boolean, oRefRes out TMP_RES_PLAN_BESTAND.REF%type) is
    vr_tmp_res  TMP_RES_PLAN_BESTAND%rowtype;
    v_sum_frei  LAGER_BESTAND.MENGE_FREI%type;
    v_sum_res   LAGER_BESTAND.MENGE_RES%type;
  begin
    oBesFlag := true;

    begin
      select * into vr_tmp_res from TMP_RES_PLAN_BESTAND where REF_PLAN=pRefPlan and REF_AR=pRefAE and REF_EINHEIT=pRefAE;

      exception
        when no_data_found then
          vr_tmp_res := null;
        when others then
          raise;
    end;

    if (vr_tmp_res.REF is not null) then
      if (lTraceLevel >= 3) then
        BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||' pRefAE='||pRefAE||' : vr_tmp_res.MENGE_FREI='||vr_tmp_res.MENGE_FREI||', vr_tmp_res.MENGE_RES='||vr_tmp_res.MENGE_RES);
      end if;

      if ((nvl (vr_tmp_res.MENGE_FREI, 0) - nvl (vr_tmp_res.MENGE_RES, 0)) >= pMenge) then
        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 1, pMenge);
      else
        oBesFlag := false;

        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 0, pMenge);
      end if;
    else
      select
          sum (MENGE_FREI), sum (MENGE_RES) into v_sum_frei, v_sum_res
        from
          LAGER_BESTAND bes
          left outer join LAGER_LE le on (bes.REF_LE is not null and le.REF=bes.REF_LE)
          inner join LAGER_LP lp on ((bes.REF_LP is not null and lp.REF=bes.REF_LP) or (bes.REF_LE is not null and lp.REF=le.REF_LP))
          inner join TMP_KOMM_PLAN_LP tmp on (tmp.REF_LP=lp.REF)
        where
          bes.STATUS='AKT' and
          bes.REF_LAGER=vr_bat_cfg.REF_LAGER and
          bes.REF_AR_EINHEIT=pRefAE and
          --Nicht in Inventur
          bes.REF_INV_POS is null and
          --Mit Menge > 0
          nvl (bes.MENGE_FREI, 0) > 0 and
          --LE nicht in Transport
          le.REF_TA is null;

      if (lTraceLevel >= 3) then
        BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||' pRefAE='||pRefAE||' : v_sum_frei='||v_sum_frei||', v_sum_res='||v_sum_res);
      end if;

      insert into TMP_RES_PLAN_BESTAND
          (REF_PLAN, REF_AR, REF_EINHEIT, MENGE_FREI, MENGE_RES)
        values
          (pRefPlan, pRefAE, pRefAE, v_sum_frei, v_sum_res)
        returning REF into vr_tmp_res.REF;

      if ((nvl (v_sum_frei, 0) - nvl (v_sum_res, 0)) >= pMenge) then
        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 1, pMenge);
      else
        oBesFlag := false;

        insert into TMP_RES_PLAN_BESTAND_USE (REF_PLAN, REF_AUF_KOPF, REF_AUF_POS, REF_RES, FLAG_RES, MENGE_USED) values (pRefPlan, prAufPos.REF_AUF_KOPF, prAufPos.REF, vr_tmp_res.REF, 0, pMenge);
      end if;
    end if;

    oRefRes := vr_tmp_res.REF;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TRACETIMESTAMP (5, '#'||$$plsql_line||' oRefRes='||oRefRes||' oBesFlag='||case when oBesFlag is null then 'null' when oBesFlag then 'true' else 'false' end);
    end if;
  end;

begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BUILD_RES_PLANUNG');
  BASE_TRACING.TraceOutput (2,'pRefBatCfg='||pRefBatCfg);
  BASE_TRACING.TraceOutput (2,'pRefSped  ='||pRefSped);
  BASE_TRACING.TraceOutput (2,'pMaxAuf   ='||pMaxAuf);

  select * into vr_bat_cfg from BATCHLAUF_CONFIG where REF=pRefBatCfg;

  begin
    select REF into pRefPlan from (select * from TMP_RES_PLAN where REF_BATCHLAUF_CONFIG=vr_bat_cfg.REF order by PLAN_DATE) where ROWNUM=1;

    exception
      when no_data_found then
        pRefPlan := null;
      when others then
        raise;
  end;

  if (pRefPlan is null) then
    insert into TMP_RES_PLAN (REF_BATCHLAUF_CONFIG) values (vr_bat_cfg.REF) returning REF into pRefPlan;
  else
    insert into TMP_RES_PLAN (REF_BATCHLAUF_CONFIG) values (vr_bat_cfg.REF) returning REF into pRefPlan;

    /*
    update TMP_RES_PLAN set AUF_COUNT=null, AUF_RES_COUNT=null, PLAN_DATE=sysdate where REF=pRefPlan;

    delete from TMP_RES_PLAN_BESTAND_USE where REF_PLAN=pRefPlan;
    delete from TMP_RES_PLAN_BESTAND where REF_PLAN=pRefPlan;
    delete from TMP_RES_PLAN_AUFTRAG where REF_PLAN=pRefPlan;
    */
  end if;

  delete from TMP_KOMM_PLAN_LP;

  for cr_lb in (select * from TMP_KOMM_PLAN_LB order by REF_LB, REF_LB_ZONE nulls first) loop
    for cr_lp in (select REF from LAGER_LP where REF_LB=cr_lb.REF_LB and (cr_lb.REF_LB_ZONE is null or REF_LB_ZONE=cr_lb.REF_LB_ZONE)) loop
      insert into TMP_KOMM_PLAN_LP (REF_LB, REF_LB_ZONE, REF_LP) values (cr_lb.REF_LB, cr_lb.REF_LB_ZONE, cr_lp.REF);
    end loop;
  end loop;

  select REF_LOCATION into v_ref_loc from LOCATION_REL_LAGER where REF_LAGER=vr_bat_cfg.REF_LAGER;

  v_cur_count := 0;
  v_auf_count := 0;

  for cr_auf in (select
                     av.VERSAND_ART, vres.SUM_MENGE_SOLL, vres.SUM_MENGE_VOR_RES, auf.*
                   from
                     AUFTRAG auf
                     inner join AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=auf.REF)
                     inner join AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=auf.REF_ART_PLANUNG)
                     inner join (select REF_AUF_KOPF, sum (MENGE_SOLL) as SUM_MENGE_SOLL, sum (MENGE_VOR_RES) as SUM_MENGE_VOR_RES from AUFTRAG_POS group by REF_AUF_KOPF) vres on (vres.REF_AUF_KOPF=auf.REF)
                     left outer join AUFTRAG_BACKLOG back on (back.REF_AUF_KOPF=auf.REF)
                   where
                     auf.STATUS='ANG' and
                     auf.REF_BATCHLAUF is null and
                     (back.NEXT_CHECK is null or back.NEXT_CHECK < sysdate) and
                     (nvl (aufplan.OPT_BATCH_PLANUNG, '0') = '1') and
                     auf.REF_LAGER=vr_bat_cfg.REF_LAGER and
                     (vr_bat_cfg.REF_SUB_MAND is null or auf.REF_SUB_MAND=vr_bat_cfg.REF_SUB_MAND)
                     --and (select count (bc.REF_AUF_KOPF) from BATCHLAUF_CONFIG_AUF_CHECK bc where bc.REF_BATCHLAUF_CONFIG=vr_bat_cfg.REF and bc.REF_AUF_KOPF=auf.REF and nvl (bc.CHECK_REASON, 0) > 0 and (bc.CREATE_AT > (sysdate - (4*60*60/86400)))) = 0
                   order by
                     case
                       when vres.SUM_MENGE_VOR_RES is not null and vres.SUM_MENGE_VOR_RES = vres.SUM_MENGE_SOLL then
                         0
                       when vres.SUM_MENGE_VOR_RES is null then
                         1
                       else
                         9
                     end asc,
                     auf.PRIO_LAGER desc nulls last,
                     auf.PRIO desc nulls last,
                     case
                       when (nvl (vr_bat_cfg.ART_SELECT_PLANUNG, '0') = '2') then
                         auf.PROCESS_DATUM
                       when (nvl (vr_bat_cfg.ART_SELECT_PLANUNG, '0') = '1') then
                         auf.BESTELL_DATUM
                       else
                         auf.AUFTRAG_DATUM
                     end asc,
                     auf.AUFTRAG_DATUM
                ) loop

    v_cur_count := v_cur_count + 1;

    v_ok := true;
    v_check := 0;

    BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': cr_auf: REF='||cr_auf.REF||', SUM_MENGE_SOLL='||cr_auf.SUM_MENGE_SOLL||', SUM_MENGE_VOR_RES='||cr_auf.SUM_MENGE_VOR_RES||', v_cur_count='||v_cur_count||', v_auf_count='||v_auf_count);

    if (v_ok) then
      --Prüfen, ob die Auftragsart in der Includeliste steht
      if (vr_bat_cfg.INCLUDE_AUFTRAG_ART is not null) and (instr (vr_bat_cfg.INCLUDE_AUFTRAG_ART, cr_auf.AUFTRAGSART) = 0) then
        --BASE_TRACING.TraceOutput (3, 'INCLUDE_AUFTRAG_ART');
        v_ok := False;
        v_check := 1;
      end if;

      --Prüfen, ob die Auftragsart in der Excludeliste steht
      if v_ok and (vr_bat_cfg.EXCLUDE_AUFTRAG_ART is not null) and (instr (vr_bat_cfg.EXCLUDE_AUFTRAG_ART, cr_auf.AUFTRAGSART) > 0) then
        --BASE_TRACING.TraceOutput (3, 'EXCLUDE_AUFTRAG_ART');
        v_ok := False;
        v_check := 2;
      end if;
    end if;

    if (v_ok) then
      --Prüfen, ob die Versandart in der Includeliste steht
      if (vr_bat_cfg.INCLUDE_VERSAND_ART is not null) and (cr_auf.VERSAND_ART is null or (instr (vr_bat_cfg.INCLUDE_VERSAND_ART, ';'||cr_auf.VERSAND_ART||';') = 0)) then
        --BASE_TRACING.TraceOutput (3, 'INCLUDE_VERSAND_ART');
        v_ok := False;
        v_check := 3;
      end if;

      --Prüfen, ob die Versandart in der Excludeliste steht
      if v_ok and (vr_bat_cfg.EXCLUDE_VERSAND_ART is not null) and cr_auf.VERSAND_ART is not null and (instr (vr_bat_cfg.EXCLUDE_VERSAND_ART, ';'||cr_auf.VERSAND_ART||';') > 0) then
        --BASE_TRACING.TraceOutput (3, 'EXCLUDE_VERSAND_ART');
        v_ok := False;
        v_check := 4;
      end if;
    end if;

    if (v_ok) and (cr_auf.REF_SUB_MAND is not null) then
      --Prüfen, ob die Auftragsart in der Includeliste steht
      if (vr_bat_cfg.INCLUDE_REF_SUB_MAND is not null) and (instr (vr_bat_cfg.INCLUDE_REF_SUB_MAND, ';'||cr_auf.REF_SUB_MAND||';') = 0) then
        --BASE_TRACING.TraceOutput (3, 'INCLUDE_REF_SUB_MAND');
        v_ok := False;
        v_check := 5;
      end if;

      --Prüfen, ob die Auftragsart in der Excludeliste steht
      if v_ok and (vr_bat_cfg.EXCLUDE_REF_SUB_MAND is not null) and (instr (vr_bat_cfg.EXCLUDE_REF_SUB_MAND, ';'||cr_auf.REF_SUB_MAND||';') > 0) then
        --BASE_TRACING.TraceOutput (3, 'EXCLUDE_REF_SUB_MAND');
        v_ok := False;
        v_check := 12;
      end if;
    end if;

    if (v_ok) and (vr_bat_cfg.OPT_SINGLE_POS_ORDER in ('1','2')) then
      declare
        v_sum_set_vpe PLS_INTEGER;
        v_sum_auf_vpe PLS_INTEGER;
      begin
        v_sum_auf_vpe := 0;

        for cr_auf_pos in (select
                               pos.MENGE_SOLL,ar.REF,ar.REF_ARTIKEL_SET,arset.OPT_KOMM_NO_SPLIT,pos.SET_POSITION
                             from
                               AUFTRAG_POS pos
                               inner join ARTIKEL ar on (ar.REF=pos.REF_AR)
                               left outer join ARTIKEL_SET arset on (arset.REF=ar.REF_ARTIKEL_SET)
                             where
                               nvl (pos.MENGE_SOLL, 0) > 0 and
                               pos.REF_AUF_KOPF=cr_auf.REF
                          ) loop
          --BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> cr_auf_pos MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', REF_ARTIKEL_SET='||cr_auf_pos.REF_ARTIKEL_SET||', OPT_KOMM_NO_SPLIT='||cr_auf_pos.OPT_KOMM_NO_SPLIT);

          if (cr_auf_pos.REF_ARTIKEL_SET is null) or (nvl (cr_auf_pos.OPT_KOMM_NO_SPLIT, '0') = '1') then
            v_sum_auf_vpe := v_sum_auf_vpe + cr_auf_pos.MENGE_SOLL;
          else
            if (nvl (cr_auf_pos.SET_POSITION, '0') = '1') then
               v_sum_set_vpe := 0;
            else
              select sum (ANZAHL * cr_auf_pos.MENGE_SOLL) into v_sum_set_vpe from ARTIKEL_SET_POS where REF_SET=cr_auf_pos.REF_ARTIKEL_SET and nvl (ANZAHL, 0) > 0;
            end if;

            v_sum_auf_vpe := v_sum_auf_vpe + v_sum_set_vpe;
          end if;

        end loop;

        --BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> v_sum_auf_vpe='||v_sum_auf_vpe);

        if (vr_bat_cfg.OPT_SINGLE_POS_ORDER = '1') then
          if (v_sum_auf_vpe <> 1) then
            --BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> OPT_SINGLE_POS_ORDER 1');
            v_ok := False;
            v_check := 6;
          end if;
        elsif (vr_bat_cfg.OPT_SINGLE_POS_ORDER = '2') then
          if (v_sum_auf_vpe = 1) then
            --BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> OPT_SINGLE_POS_ORDER 2');
            v_ok := False;
            v_check := 7;
          end if;
        end if;
      end;
    end if;

    if (v_ok) and (pRefSped is not null) then
      --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
      select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap where ((ap.REF_SPED is not null and ap.REF_SPED=pRefSped) or av.REF_SPED=pRefSped) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=cr_auf.REF;

      --BASE_TRACING.TraceOutput (3,'REF_SPED v_count='||v_count);

      if (v_count = 0) then
        v_ok := False;
        v_check := 8;
      end if;
    end if;

    if (v_ok) then
      --Prüfen, ob es Big Item Artikel in dem Auftrag gibt
      select
          count (*)
        into
          v_count
        from
          AUFTRAG_POS ap
          inner join ARTIKEL_EINHEIT ae on (ae.REF=ap.REF_AR_EINHEIT)
          left outer join ARTIKEL_EINHEIT_REL_LAGER ael on (ael.REF_AR_EINHEIT=ae.REF and REF_LOCATION=v_ref_loc)
        where
          ap.REF_AUF_KOPF=cr_auf.REF and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and (coalesce (ael.OPT_BIG_ITEM, ae.OPT_BIG_ITEM, '0')='1');

      --BASE_TRACING.TraceOutput (3,'OPT_BIG_ITEM v_count='||v_count);

      if (v_count = 0) and (nvl (vr_bat_cfg.OPT_BIG_ITEM_ONLY, '0') = '1') then
        v_ok := false;
      elsif (v_count > 0) and (nvl (vr_bat_cfg.OPT_BIG_ITEM, '0') = '0') then
        if (nvl (vr_bat_cfg.OPT_TEIL_PLANUNG, '0') = '0') then
          v_ok := false;
          v_check := 9;
        end if;
      end if;
    end if;

    if (v_ok) then
      --Prüfen, ob es Sperrgut-Artikel in dem Auftrag gibt
      select
          count (*)
        into
          v_count
        from
          AUFTRAG_POS ap
          inner join ARTIKEL_EINHEIT ae on (ae.REF=ap.REF_AR_EINHEIT)
          left outer join ARTIKEL_EINHEIT_REL_LAGER ael on (ael.REF_AR_EINHEIT=ae.REF and REF_LOCATION=v_ref_loc)
        where
          ap.REF_AUF_KOPF=cr_auf.REF and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and (coalesce (ael.OPT_SPERRGUT, ae.OPT_SPERRGUT, '0')='1');

      --BASE_TRACING.TraceOutput (3,'OPT_SPERRGUT v_count='||v_count);

      if (v_count = 0) and (nvl (vr_bat_cfg.OPT_SPERRGUT_ONLY, '0') = '1') then
        v_ok := false;
      elsif (v_count > 0) and (nvl (vr_bat_cfg.OPT_SPERRGUT, '0') = '0') then
        if (nvl (vr_bat_cfg.OPT_TEIL_PLANUNG, '0') = '0') then
          v_ok := false;
          v_check := 10;
        end if;
      end if;
    end if;

    if (v_ok) then
      declare
        v_land AUFTRAG_ADR.LAND_ISO%type;
      begin
        select nvl (LAND_ISO, 'DE') into v_land from AUFTRAG_ADR where REF=cr_auf.REF_LIEFER_ADR;

        --BASE_TRACING.TraceOutput (3,'vr_auf_adr.LAND_ISO='||vr_auf_adr.LAND_ISO||', vr_bat_cfg.INCLUDE_COUNTRY='||vr_bat_cfg.INCLUDE_COUNTRY||', vr_bat_cfg.EXCLUDE_COUNTRY='||vr_bat_cfg.EXCLUDE_COUNTRY);

        --Prüfen, ob das Land in der Includeliste steht
        if (vr_bat_cfg.INCLUDE_COUNTRY is not null) and (instr (vr_bat_cfg.INCLUDE_COUNTRY, v_land) = 0) then
          v_ok := False;
          v_check := 11;
        end if;

        --Prüfen, ob das Land in der Excludeliste steht
        if v_ok and (vr_bat_cfg.EXCLUDE_COUNTRY is not null) and (instr (vr_bat_cfg.EXCLUDE_COUNTRY, v_land) > 0) then
          v_ok := False;
          v_check := 12;
        end if;
      end;
    end if;

    if (v_ok) then
      --BASE_TRACING.TraceOutput (3,'vr_bat_cfg.INCLUDE_SPEDITION='||vr_bat_cfg.INCLUDE_SPEDITION||', vr_bat_cfg.EXCLUDE_SPEDITION='||vr_bat_cfg.EXCLUDE_SPEDITION);

      --Prüfen, ob der Spediteur in der Includeliste steht
      if (vr_bat_cfg.INCLUDE_SPEDITION is not null) then
        --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
        --Änderung BL am 22.10.20 instr wurde vorher auf = 0 abgefragt

        select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap, SPEDITIONEN sped where (instr (vr_bat_cfg.INCLUDE_SPEDITION, ';'||sped.NAME||';')>0) and ((ap.REF_SPED is not null and sped.REF=ap.REF_SPED) or sped.REF=av.REF_SPED) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=cr_auf.REF;

        --BASE_TRACING.TraceOutput (3,'INCLUDE_SPEDITION v_count='||v_count);

        if (v_count = 0) then
          v_ok := False;
          v_check := 13;
        end if;
      end if;

      --Prüfen, ob der Spediteur in der Excludeliste steht
      if (v_ok and (vr_bat_cfg.EXCLUDE_SPEDITION is not null)) then
        --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
        select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap, SPEDITIONEN sped where (instr ( vr_bat_cfg.EXCLUDE_SPEDITION, ';'||sped.NAME||';') > 0) and sped.REF=(nvl (ap.REF_SPED, av.REF_SPED)) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=cr_auf.REF;

        --BASE_TRACING.TraceOutput (3,'EXCLUDE_SPEDITION v_count='||v_count);

        if (v_count > 0) then
          v_ok := False;
          v_check := 14;
        end if;
      end if;
    end if;

    BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF||', v_check='||v_check);

    if (v_ok) then
      BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF);

      delete from TMP_RES_PLAN_BESTAND_USE where REF_PLAN=pRefPlan and REF_AUF_KOPF=cr_auf.REF;

      for cr_auf_pos in (select
                             ap.*
                           from
                             AUFTRAG_POS ap
                             inner join ARTIKEL ar on (ar.REF=ap.REF_AR)
                           where
                             ap.REF_AUF_KOPF=cr_auf.REF and
                             ap.CROSSDOCK_BESTELL_NR is null and
                             (nvl (ap.SET_POSITION, '0') = '0') and
                             (nvl (ap.TEXT_POSITION, '0') = '0')
                           order by
                             ap.POS_NR
                         ) loop
        v_menge_use := cr_auf_pos.MENGE_SOLL - nvl (cr_auf_pos.MENGE_GESAMT, 0);

        if (v_menge_use > 0) then
          select * into vr_ar from ARTIKEL where REF=cr_auf_pos.REF_AR;
          select * into vr_ae from ARTIKEL_EINHEIT where REF=cr_auf_pos.REF_AR_EINHEIT;

          if (nvl (vr_ae.OPT_MULTI_COLLI, '0') = '1') then
            declare
              v_colli_pos_use INTEGER;
            begin
              for cr_colli in (select * from ARTIKEL_EINHEIT_COLLI where REF_MASTER_AR_EINHEIT=vr_ae.REF) loop
                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_colli : REF_SET_AR_EINHEIT='||cr_colli.REF_SET_AR_EINHEIT||', MENGE='||cr_colli.MENGE);
                end if;

                if (nvl (cr_colli.MENGE, 0) > 0) then
                  v_colli_pos_use := v_menge_use * cr_colli.MENGE;

                  if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_colli_pos_use='||v_colli_pos_use); end if;

                  ResPlanBestand (cr_auf_pos, cr_colli.REF_SET_AR_EINHEIT, v_colli_pos_use, v_bes, v_ref_res);

                  if not (v_bes) then
                    if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF||' -> v_ok=false'); end if;

                    v_ok := false;
                  end if;
                end if;
              end loop;
            end;
          elsif (vr_ar.REF_ARTIKEL_SET is not null) then
            declare
              vr_set_ae     ARTIKEL_EINHEIT%rowtype;
              v_set_pos_use integer;
            begin
              ResPlanBestand (cr_auf_pos, cr_auf_pos.REF_AR, cr_auf_pos.REF_EINHEIT, v_menge_use, v_bes, v_ref_res);

              if not (v_bes) then
                declare
                  vr_set        ARTIKEL_SET%rowtype;
                  vr_set_ae     ARTIKEL_EINHEIT%rowtype;
                  v_set_pos_use integer;
                begin
                  select * into vr_set from ARTIKEL_SET where REF=vr_ar.REF_ARTIKEL_SET;

                  if (nvl (vr_set.OPT_KOMM_NO_SPLIT, '0') = '1') then
                    --Das Set wird nicht weiter aufgelöst
                    if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF||' -> v_ok=false'); end if;

                    v_ok := false;
                    v_check := 20;
                  else
                    for cr_set_ar in (select * from ARTIKEL_SET_POS where REF_SET=vr_ar.REF_ARTIKEL_SET and nvl (ANZAHL, 0) > 0 order by ANZAHL desc) loop
                      select * into vr_set_ae from ARTIKEL_EINHEIT where REF=cr_set_ar.REF_AR_EINHEIT;

                      if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_set_ar : ANZAHL='||cr_set_ar.ANZAHL||', BRUCH_FAKTOR='||cr_set_ar.BRUCH_FAKTOR||', vr_set_ae : REF='||vr_set_ae.REF||', REF_AR='||vr_set_ae.REF_AR||', REF_EINHEIT='||vr_set_ae.REF_EINHEIT); end if;

                      if (nvl (cr_set_ar.ANZAHL, 0) > 0) then
                        --Prüfen, ob ein Bruchfaktor definiert ist
                        if (nvl (cr_set_ar.BRUCH_FAKTOR, 0.0) > 0) then
                          --Es wird immer eine ganzzahlige Menge benötigt
                          v_set_pos_use := ceil (v_menge_use * cr_set_ar.ANZAHL + (v_menge_use * cr_set_ar.ANZAHL * (cr_set_ar.BRUCH_FAKTOR / 100.0)));
                        else
                          --Es wird immer eine ganzzahlige Menge benötigt
                          v_set_pos_use := ceil (v_menge_use * cr_set_ar.ANZAHL);
                        end if;

                        if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_pos_use='||v_set_pos_use); end if;

                        ResPlanBestand (cr_auf_pos, vr_set_ae.REF_AR, vr_set_ae.REF_EINHEIT, v_set_pos_use, v_bes, v_ref_res);

                        if not (v_bes) then
                          if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF||' -> v_ok=false'); end if;

                          v_ok := false;
                        end if;
                      end if;
                    end loop;
                  end if;
                end;
              end if;
            end;
          else
            ResPlanBestand (cr_auf_pos, cr_auf_pos.REF_AR, cr_auf_pos.REF_EINHEIT, v_menge_use, v_bes, v_ref_res);

            if not (v_bes) then
              declare
                v_in_bes        boolean := false;
                vr_res          TMP_RES_PLAN_BESTAND%rowtype;
                v_ref_in_res    TMP_RES_PLAN_BESTAND.REF%type;
                v_menge_res     integer;
                v_menge_use_in  integer;
                v_frei          integer;
                v_res           integer;
              begin
                select * into vr_res from TMP_RES_PLAN_BESTAND where REF=v_ref_res;

                v_menge_res := v_menge_use - (nvl (vr_res.MENGE_FREI, 0) - nvl (vr_res.MENGE_RES, 0));

                for cr_in_ae in (select * from ARTIKEL_EINHEIT where REF_INHALT in (select REF from ARTIKEL_EINHEIT where STATUS='AKT' and REF_AR=cr_auf_pos.REF_AR and REF_EINHEIT=cr_auf_pos.REF_EINHEIT)) loop
                  if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_in_ae REF='||cr_in_ae.REF||', INHALT_ANZAHL='||cr_in_ae.INHALT_ANZAHL||' v_menge_res='||v_menge_res); end if;

                  v_menge_use_in := ceil (v_menge_res / cr_in_ae.INHALT_ANZAHL);

                  ResPlanBestand (cr_auf_pos, cr_in_ae.REF_AR, cr_in_ae.REF_EINHEIT, v_menge_use_in, v_in_bes, v_ref_in_res);

                  if (v_in_bes) then
                    if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': upd REF='||v_ref_res||' -> '||(v_menge_use_in * cr_in_ae.INHALT_ANZAHL)); end if;

                    update TMP_RES_PLAN_BESTAND
                      set
                        MENGE_FREI=nvl (MENGE_FREI, 0) + (v_menge_use_in * cr_in_ae.INHALT_ANZAHL)
                      where
                        REF=v_ref_res
                      returning MENGE_FREI, MENGE_RES into v_frei, v_res;

                    if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': upd REF='||v_ref_res||' = '||v_frei||', '||v_res); end if;

                    exit;
                  end if;
                end loop;

                if (v_in_bes) then
                  v_bes := true;
                end if;
              end;

              if not (v_bes) then
                if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF||' -> v_ok=false'); end if;

                v_ok := false;
              end if;
            end if;
          end if;
        end if;
      end loop;

      if (v_ok) then
        v_auf_count := v_auf_count + 1;

        insert into TMP_RES_PLAN_AUFTRAG (REF_PLAN, REF_AUF_KOPF, PRIO, SUM_VPE) values (pRefPlan, cr_auf.REF, cr_auf.PRIO_LAGER, 1);

        for cr_use in (select * from TMP_RES_PLAN_BESTAND_USE where REF_PLAN=pRefPlan and REF_AUF_KOPF=cr_auf.REF) loop
          if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_use : REF_AUF_POS='||cr_use.REF_AUF_POS||', REF_RES='||cr_use.REF_RES||', MENGE_USED='||cr_use.MENGE_USED); end if;

          update TMP_RES_PLAN_BESTAND set MENGE_RES=nvl (MENGE_RES, 0) + cr_use.MENGE_USED where REF=cr_use.REF_RES;
        end loop;

        update TMP_RES_PLAN set AUF_RES_COUNT=nvl (AUF_RES_COUNT, 0) + 1 where REF=pRefPlan;
      else
        insert into TMP_RES_PLAN_AUFTRAG (REF_PLAN, REF_AUF_KOPF, PRIO, SUM_VPE) values (pRefPlan, cr_auf.REF, cr_auf.PRIO_LAGER, 0);

        for cr_use in (select * from TMP_RES_PLAN_BESTAND_USE where REF_PLAN=pRefPlan and REF_AUF_KOPF=cr_auf.REF) loop
          if (cr_use.FLAG_RES = 0) then
            update TMP_RES_PLAN_BESTAND set MENGE_USE=nvl (MENGE_USE, 0) + cr_use.MENGE_USED, AUF_POS_USE=nvl (AUF_POS_USE, 0) + 1 where REF=cr_use.REF_RES;
          end if;
        end loop;
      end if;

      update TMP_RES_PLAN set AUF_COUNT=nvl (AUF_COUNT, 0) + 1 where REF=pRefPlan;
    end if;

    if (nvl (v_check, 0) > 0) then
      insert into BATCHLAUF_CONFIG_AUF_CHECK (REF_BATCHLAUF_CONFIG, REF_AUF_KOPF, CHECK_REASON) values (vr_bat_cfg.REF, cr_auf.REF, v_check);
    end if;

    --Nur 10 mal mehr prüfen, als geplant werden sollen, sparte Laufzeit
    exit when pMaxAuf is not null and (((pMaxAuf > 50) and v_auf_count > (pMaxAuf * 10)) or (v_auf_count > (pMaxAuf * 40)));
  end loop;

  if (lTraceLevel >= 3) then BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_auf_count='||v_auf_count); end if;

  if (BASE_TRACING.GETTRACELEVEL >= 9) then
    for cr_res_bes in (select * from TMP_RES_PLAN_BESTAND where REF_PLAN=pRefPlan) loop
      BASE_TRACING.TraceOutput (9, '#'||$$plsql_line||': cr_res_bes : REF_AR='||cr_res_bes.REF_AR||', REF_EINHEIT='||cr_res_bes.REF_EINHEIT||', MENGE_FREI='||cr_res_bes.MENGE_FREI||', MENGE_RES='||cr_res_bes.MENGE_RES||', MENGE_USE='||cr_res_bes.MENGE_USE||', AUF_POS_USE='||cr_res_bes.AUF_POS_USE);
    end loop;
  end if;

  BASE_TRACING.TraceOutput (2, 'pRefPlan='||pRefPlan);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_TOUR (prAuftrag in AUFTRAG%ROWTYPE, oOk OUT INTEGER, oErrorCode OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is
  aktlevel    PLS_INTEGER;

  vr_auf_adr  AUFTRAG_ADR%ROWTYPE;
  vr_vers     AUFTRAG_VERSAND%ROWTYPE;

  v_ref_tour  WA_REL_TOUR.REF%TYPE;
  v_ref_err   FEHLER_LISTE.REF%TYPE;
  v_stat      FEHLER_LISTE.FEHLER_STATUS%TYPE;
  v_err_txt   FEHLER_LISTE.FEHLER_TEXT%TYPE;

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_TOUR');
  BASE_TRACING.TraceOutput (2,'REF='||prAuftrag.REF);

  select * into vr_vers from AUFTRAG_VERSAND where REF_AUF_KOPF=prAuftrag.REF;

  v_stat := NULL;
  v_err_txt := NULL;

  if (vr_vers.REF_TOUR is null) Then
    begin
  	  select * into vr_auf_adr from AUFTRAG_ADR where STATUS='AKT' and REF_AUF_KOPF=prAuftrag.REF and ART='KUNDEN_ADR';

    exception
      when no_data_found then
  	    v_stat    := 'ADR';
        v_err_txt := 'Die Kundenanschrift ist nicht definiert';

  	  when others then
  	    raise;
    end;

    if (v_stat is null) Then
    	begin
      	select REF into v_ref_tour from WA_REL_TOUR where REF_MAND=prAuftrag.REF_MAND and REF_LAGER=prAuftrag.REF_LAGER and TOUR_NR=vr_vers.TOUR_NR and ROWNUM=1;

  		  exception
    		  when no_data_found then
  	  	    v_stat    := 'TOUR';
            v_err_txt := 'Für die Tour '||vr_vers.TOUR_NR||' gibt es keine passende WA-Relation';

  			when others then
  			  raise;
    	end;
    end if;

    if (res = 0) and (oErrorCode = 0) then
      if (v_stat is not null) then
        oOk := 0;

        insert into FEHLER_LISTE
            (REF_ID, FEHLER_STATUS, FEHLER_TEXT)
          values
            (prAuftrag.REF, v_stat, v_err_txt)
          returning REF into v_ref_err;

        update AUFTRAG set FEHLER_STATUS=v_stat, REF_FEHLER=v_ref_err where REF=prAuftrag.REF;
      else
        oOk := 1;

        update AUFTRAG_VERSAND set REF_TOUR = v_ref_tour where REF_AUF_KOPF=vr_vers.REF_AUF_KOPF;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oOk='||oOk);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function RESET_RESERVIERUNG (pID in LAGER_RES_BESTAND.RES_ID%TYPE) return INTEGER is
  aktlevel  PLS_INTEGER;

  v_res     INTEGER;
  v_frei    INTEGER;

  res       INTEGER;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.RESET_RESERVIERUNG');
  BASE_TRACING.TraceOutput (2,'pID='||pID);

  begin
    for vr in (select * from LAGER_RES_BESTAND where RES_ID=pID order by REF_SET_RES nulls last) loop
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr.REF='||vr.REF||' -> vr.MENGE_RES='||vr.MENGE_RES);
      end if;

      if (vr.REF_AUF_POS_LT is not null) then
        update AUFTRAG_POS_LT set LT_ANZ_GEPLANT=null where REF=vr.REF_AUF_POS_LT;
      elsif (vr.MENGE_RES is not null) then
        if (vr.REF_BES_SUM is not null) then
          select MENGE_FREI, MENGE_RES into v_frei,v_res from VQ_LAGER_BESTAND_SUMME_PLAN where REF=vr.REF_BES_SUM;
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr.REF_BES_SUM='||vr.REF_BES_SUM||' -> MENGE_RES='||v_res||' -> MENGE_FREI='||v_frei);
          end if;

          if (vr.MENGE_RES > v_res) then
            raise_application_error (-20105, 'RES-Fehler RES='||v_res);
          end if;

          --Reservierte Menge im Summenbestand wieder zurücknehmen
          update LAGER_BESTAND_SUMME_RES
            set
              MENGE_RES=NVL (MENGE_RES,0) -  vr.MENGE_RES
            where
              REF_BES_SUM=vr.REF_BES_SUM
            returning MENGE_RES into v_res;

          if (v_res<0) then
            raise_application_error (-20102, 'RES-Fehler RES='||v_res);
          end if;
        end if;

        if (vr.REF_BESTAND is not null) then
          --Reservierte Menge im Bestand wieder zurücknehmen
         update LAGER_BESTAND
            set
              MENGE_RES=NVL (MENGE_RES,0) -  vr.MENGE_RES
            where
              REF=vr.REF_BESTAND
            returning MENGE_RES into v_res;

            if (v_res<0) then
              raise_application_error (-20100, 'RES-Fehler RES='||v_res);
            end if;
        end if;
      end if;

      update LAGER_RES_BESTAND set REF_BESTAND=NULL, REF_BES_SUM=NULL where REF=vr.REF;

      if (vr.REF_SAMMEL_RES is not null) then
        declare
          vr_s      LAGER_RES_BESTAND%ROWTYPE;
          v_s_res   LAGER_RES_BESTAND.MENGE_RES%TYPE;
        begin
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr.REF_SAMMEL_RES='||vr.REF_SAMMEL_RES||' -> vr.MENGE_USED='||vr.MENGE_USED);
          end if;

          select * into vr_s from LAGER_RES_BESTAND where REF=vr.REF_SAMMEL_RES;

          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_s.REF='||vr_s.REF||' -> vr_s.MENGE_USED='||vr_s.MENGE_USED);
          end if;

          --Prüfen, ob die benötige Menge in der Sammelres nur noch für diese Reservierung gilt
          if (vr_s.MENGE_USED > vr.MENGE_USED) Then
            --Wenn nicht, dann die benötige Menge reduzieren
            vr_s.MENGE_USED := vr_s.MENGE_USED - vr.MENGE_USED;

            --Ermitteln, wie viele Umverpackungen dafür noch benötigt werden
            select ceil (vr_s.MENGE_USED / INHALT_ANZAHL) into v_s_res from ARTIKEL_EINHEIT where REF=(select REF_AR_EINHEIT from LAGER_BESTAND_SUMME where REF=vr_s.REF_BES_SUM);

            if (lTraceLevel >= 5) then
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_s_res='||v_s_res||', vr_s.MENGE_RES='||vr_s.MENGE_RES);
            end if;

            --Zur Sicherheit, wenn INHALT_ANZAHL = 0 oder null wäre
            if (v_s_res is null) then
              v_s_res := vr_s.MENGE_RES;
            end if;

            --Sammelreservierung updaten
            if (v_s_res = vr_s.MENGE_RES) then
              update LAGER_RES_BESTAND set MENGE_USED=vr_s.MENGE_USED where REF=vr_s.REF;
            else
              update LAGER_RES_BESTAND set MENGE_RES=v_s_res, MENGE_USED=vr_s.MENGE_USED where REF=vr_s.REF;
            end if;
          else
            --Wenn ja, die Sammelreservierung löschen
            v_s_res := 0;

            /*War nur zum Debuggen
            for vr_temp in (select * from LAGER_RES_BESTAND where REF_SAMMEL_RES=vr_s.REF) loop
              BASE_TRACING.TraceOutput (2,'vr_temp.REF='||vr_temp.REF||' -> vr_temp.MENGE_USED='||vr_temp.MENGE_USED);
            end loop;
            */

            update LAGER_RES_BESTAND set REF_SAMMEL_RES=null where REF=vr.REF;

            update LAGER_RES_BESTAND set MENGE_RES=null where REF=vr_s.REF;

            delete from LAGER_RES_BESTAND where REF=vr_s.REF;
          end if;

          if (v_s_res <> vr_s.MENGE_RES) then
            if (vr_s.REF_BES_SUM is not null) then
              select MENGE_FREI, MENGE_RES into v_frei,v_res from VQ_LAGER_BESTAND_SUMME_PLAN where REF=vr_s.REF_BES_SUM;

              if (lTraceLevel >= 5) then
               BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_s.REF_BES_SUM='||vr_s.REF_BES_SUM||' -> MENGE_RES='||v_res||' -> MENGE_FREI='||v_frei);
              end if;

              if ((vr_s.MENGE_RES - v_s_res) > v_res) then
                raise_application_error (-20105, 'RES-Fehler RES='||v_res);
              end if;

              --Reservierte Menge im Summenbestand wieder zurücknehmen
              update LAGER_BESTAND_SUMME_RES
                set
                  MENGE_RES=NVL (MENGE_RES,0) - (vr_s.MENGE_RES - v_s_res)
                where
                  REF_BES_SUM=vr_s.REF_BES_SUM
                returning MENGE_RES into v_res;

              if (v_res<0) then
                raise_application_error (-20107, 'RES-Fehler RES='||v_res);
              end if;
            end if;

            if (vr_s.REF_BESTAND is not null) then
              --Reservierte Menge im Bestand wieder zurücknehmen
             update LAGER_BESTAND
                set
                  MENGE_RES=NVL (MENGE_RES,0) - (vr_s.MENGE_RES - v_s_res)
                where
                  REF=vr_s.REF_BESTAND
                returning MENGE_RES into v_res;

                if (v_res<0) then
                  raise_application_error (-20108, 'RES-Fehler RES='||v_res);
                end if;
            end if;
          end if;
        end;
      end if;

      --Reservierung löschen
      delete from LAGER_RES_BESTAND where REF=vr.REF;
    end loop;

    exception
      when others then
        raise;
  end;

  if (res = 0) then
    delete from TMP_KOMM_RES where RES_ID=pID;

    --Die Vorreservierung wieder herstellen
    for cr_vor in (select * from TMP_VOR_RES where RES_ID=pID) loop
      if (nvl (cr_vor.MENGE_RES, 0) > 0) then
        UPDATE LAGER_BESTAND_SUMME_RES
          SET
            MENGE_VOR_RES=nvl (MENGE_VOR_RES, 0) + cr_vor.MENGE_RES
          WHERE
             REF_BES_SUM=cr_vor.REF_BES_SUM;

        update LAGER_VOR_RES_BESTAND set MENGE_RES=cr_vor.MENGE_RES where REF=cr_vor.REF_VOR_RES;
      end if;
    end loop;

    delete from TMP_VOR_RES where RES_ID=pID;
  end if;

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BucheTeilMenge
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BucheTeilMenge (pRefBes     in     LAGER_BESTAND.REF%type,
                         pRefPlan    in     TMP_KOMM_PLAN_LB.REF%type,
                         pRefAufPos  in     AUFTRAG_POS.REF%type,
                         pMinMHD     in     DATE,
                         pID         in     LAGER_RES_BESTAND.RES_ID%TYPE,  --Die ID des Vorganges
                         rPlanArt    in     AUFTRAG_ART_PLANUNG%rowtype,
                         pIsVollPAL  in     BOOLEAN,
                         poRestMenge in out PLS_INTEGER) return number is

  vr_lb       LAGER_LB%rowtype;
  vr_lp       LAGER_LP%rowtype;
  vr_le       LAGER_LE%rowtype;
  vr_bes      LAGER_BESTAND%rowtype;
  vr_bes_sum  VQ_LAGER_BESTAND_SUMME_PLAN%rowtype;

  vr_auf_pos  AUFTRAG_POS%rowtype := null;

  vr_we       WARENEINGANG%rowtype := null;
  vr_plan     TMP_KOMM_PLAN_LB%rowtype := null;

  v_voll_pal  LAGER_RES_BESTAND.FLAG_VOLLPAL%type;
  v_ref_lp    LAGER_LP.REF%type := null;
  v_ref_lb    LAGER_LB.REF%type := null;

  v_count     PLS_INTEGER;
  v_bes_menge PLS_INTEGER;
  v_komm_gw   PLS_INTEGER;

  v_test      PLS_INTEGER;
  aktlevel    PLS_INTEGER;
  res         number;
begin
  res := 0;

  vr_lb := null;
  vr_le := null;
  vr_lp := null;

  aktlevel := BASE_TRACING.ENTERPROC ('BucheTeilMenge');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pRefBes     ='||pRefBes);
    BASE_TRACING.TraceOutput (2,'pRefPlan    ='||pRefPlan);
    BASE_TRACING.TraceOutput (2,'pRefAufPos  ='||pRefAufPos);
    BASE_TRACING.TraceOutput (2,'pMinMHD     ='||to_char (pMinMHD, 'dd.mm.yyyy'));
    BASE_TRACING.TraceOutput (2,'pID         ='||pID);
    BASE_TRACING.TraceOutput (2,'pIsVollPAL  ='||case when pIsVollPAL then 'True' else 'False' end);
    BASE_TRACING.TraceOutput (2,'poRestMenge ='||poRestMenge);
  end if;

  select * into vr_bes from LAGER_BESTAND where REF=pRefBes;
  select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=vr_bes.REF_BES_SUM;

  if (pRefAufPos is not null) then
    select * into vr_auf_pos from AUFTRAG_POS where REF=pRefAufPos;
  end if;

  if (pRefPlan is not null) then
    select * into vr_plan from TMP_KOMM_PLAN_LB where REF=pRefPlan;
  end if;

  if (vr_bes.REF_WE_POS is not null) then
    select * into vr_we from WARENEINGANG where REF=(select REF_WE froM WE_POS where REF=vr_bes.REF_WE_POS);
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_bes:  REF='||vr_bes.REF||', REF_LP='||vr_bes.REF_LP||', REF_LE='||vr_bes.REF_LE||', REF_LB='||vr_bes.REF_LB);
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_plan: REF_LB='||vr_plan.REF_LB||', MIN_VPE='||vr_plan.MIN_VPE||', MAX_VPE='||vr_plan.MAX_VPE||', vr_we.STATUS='||vr_we.STATUS);
  end if;

  --Der Bestand muss bereits eingelagert und in einem zulässigen Bereich liegen
  if (vr_bes.REF_LP is not null) then
    select * into vr_lp from LAGER_LP where REF=vr_bes.REF_LP;

    --Bereich des Lagerplatzes
    select * into vr_lb from LAGER_LB where REF=vr_lp.REF_LB;
  elsif (vr_bes.REF_LE is not null) then
    select * into vr_le from LAGER_LE where REF=vr_bes.REF_LE;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_le: REF_LP='||vr_le.REF_LP||', REF_LB='||vr_le.REF_LB||', REF_TA='||vr_le.REF_TA);
    end if;

    if (vr_le.REF_LP is not null) then
      select * into vr_lp from LAGER_LP where REF=vr_le.REF_LP;

      --Bereich des Lagerplatzes des LEs
      select * into vr_lb from LAGER_LB where REF=vr_lp.REF_LB;
    elsif (vr_le.REF_LB is not null) then
      vr_lp := null;

      --Bereich des LEs
      select * into vr_lb from LAGER_LB where REF=vr_le.REF_LB;
    end if;
  elsif (vr_bes.REF_LB is not null) then
    --Bereich des Bestandes
    select * into vr_lb from LAGER_LB where REF=vr_bes.REF_LB;
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_lb: REF='||vr_lb.REF||', LB_ART='||vr_lb.LB_ART);
  end if;

  if (vr_we.REF is not null and vr_we.STATUS<>'ABG') then
    --Noch nicht eingelagert
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 4, 'WE noch nicht abgeschlossen');
  elsif vr_lb.REF is null then
    --Kein Lagerbericht geht gar nicht
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 1, 'Ware noch nicht eingelagert');
  elsif (vr_lb.LB_ART in ('WE') and vr_plan.REF_LB is null) then
    --Noch nicht eingelagert
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 2, 'Ware steht noch im WE');
  elsif (vr_lb.LB_ART in ('WA') and vr_plan.REF_LB is null) then
    --Noch nicht eingelagert
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 3, 'Ware noch nicht rückgelagert');
  elsif (vr_lp.REF is null) then
    --Wenn die Ware keinen Lagerplatz zugeordnet ist, kann sie nicht gepickt werden
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 6, 'Ware noch nicht im Zugriff (LP)');
  elsif (vr_plan.REF_LB is null) then
    --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 6, 'Ware noch nicht im Zugriff (LB)');
  elsif (vr_plan.MIN_VPE is not null and poRestMenge < vr_plan.MIN_VPE) then
    --In dem Lagerbericht darf ab min. MIN_VPE kommissioniert werden
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 12, PA_LB.GET_BEREICH_NAME (vr_lb.REF)||':Geforderte Menge kleiner als '||vr_plan.MIN_VPE);
  elsif (vr_plan.MAX_VPE is not null and poRestMenge > vr_plan.MAX_VPE) then
    --In dem Lagerbericht darf bis max. MAX_VPE kommissioniert werden
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 13, PA_LB.GET_BEREICH_NAME (vr_lb.REF)||':Geforderte Menge grösser als '||vr_plan.MAX_VPE);
  elsif (vr_auf_pos.CHARGE is not null and not nullcomparestr (vr_bes.CHARGE,vr_auf_pos.CHARGE)) then
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||':13');
    end if;

    --In dem Lagerbericht darf bis max. MAX_VPE kommissioniert werden
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 13, PA_LB.GET_BEREICH_NAME (vr_lb.REF)||':Charge passt nicht überein');
  elsif (vr_le.REF is not null and vr_le.REF_TA is not null) then
    --Die LE darf nicht für einen Transport verplant sein
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 28, 'LE für Transport vorgesehen');
  else
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': poRestMenge='||poRestMenge||', vr_bes: REF='||vr_bes.REF||', MENGE_FREI='||vr_bes.MENGE_FREI||', MENGE_RES='||vr_bes.MENGE_RES);
    end if;

    --Zu reservierende Menge bestimmen
    if pIsVollPAL then
      v_bes_menge := vr_bes.MENGE_FREI;
    elsif (poRestMenge > (nvl (vr_bes.MENGE_FREI, 0) - nvl (vr_bes.MENGE_RES, 0))) then
      v_bes_menge := (nvl (vr_bes.MENGE_FREI, 0) - nvl (vr_bes.MENGE_RES, 0));
    else
      v_bes_menge := poRestMenge;
    end if;

    v_komm_gw := (vr_bes.NETTO_GEWICHT / vr_bes.MENGE_FREI) * v_bes_menge;

    v_voll_pal := '0';

    --Prüfen ob es eine Vollpaletten Entnahme zulässig und möglich ist
    if (nvl (vr_plan.OPT_VOLL_PAL ,'0') = '1') and  (substr (nvl (vr_lb.KOMM_OPT, '00000000'), PA_KOMM_PLANUNG.PLAN_VOLL_PAL, 1) = '1') and (nvl (rPlanArt.OPT_VOLL_PAL_PLAN, '0') > '0') then
      --Der Bestand muss auf einer LE stehen, es darf nichts reserviert sein und der gesamt freie Bestand wird benötigt
      if (vr_bes.REF_LE is not null) and (nvl (vr_bes.MENGE_RES, 0) = 0) and (pIsVollPAL or (v_bes_menge = vr_bes.MENGE_FREI)) then
        --Prüfen, ob sich noch weitere Bestände auf der LE befinden
        select count (*) into v_count from LAGER_BESTAND where REF_LE=vr_bes.REF_LE and REF<>vr_bes.REF;

        if (v_count = 0) then
          --Wenn nicht, ist das eine Vollpalettenentnahme
          v_voll_pal := '1';
        end if;
      end if;
    end if;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_bes_menge='||v_bes_menge||', v_voll_pal='||v_voll_pal||', rPlanArt.OPT_BES_RES='||rPlanArt.OPT_BES_RES);
    end if;

    if (vr_bes.REF_LP is not null) then
      select lp.REF, lp.REF_LB into v_ref_lp, v_ref_lb from LAGER_LP lp where lp.REF=vr_bes.REF_LP;
    elsif (vr_bes.REF_LE is not null) then
      select lp.REF, lp.REF_LB into v_ref_lp, v_ref_lb from LAGER_LE le left outer join LAGER_LP lp on (lp.REF=le.REF_LP) where le.REF=vr_bes.REF_LE;
    end if;

    insert into TMP_KOMM_RES (RES_ID, REF_LB, REF_LE, REF_LP, REF_BAT_CONFIG) values (pID, v_ref_lb, vr_bes.REF_LE, v_ref_lp, vr_plan.REF_BAT_CONFIG);

    if (pID is null) then
      null;
    else
      if (vr_bes.REF_AR_EINHEIT = vr_bes_sum.REF_AR_EINHEIT) then
        if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': insert VPE res'); end if;

        insert into LAGER_RES_BESTAND
            (RES_ID, REF_BES_SUM,
             REF_BESTAND,
             REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, FLAG_VOLLPAL, REF_LB, REF_LE, REF_LP, REF_AUFTRAG_ART_REL_LB)
          VALUES
            (pID, vr_bes.REF_BES_SUM,
             case when nvl (rPlanArt.OPT_BES_RES, '0')='1' then vr_bes.REF else null end,
             pRefAufPos, v_bes_menge, vr_bes.REF_AR_EINHEIT, v_bes_menge, vr_bes.REF_AR_EINHEIT, v_komm_gw, pMinMHD, v_voll_pal, v_ref_lb, vr_bes.REF_LE, vr_bes.REF_LP, vr_plan.REF_AUFTRAG_ART_REL_LB);
      else
        declare
          vr_res LAGER_RES_BESTAND%rowtype;
        begin
          if (nvl (rPlanArt.OPT_BES_RES, '0')='0') then
            vr_res := null;
          else
            begin
              --Prüfen ob es schon einen passenden Eintrag in LAGER_RES_BESTAND gibt
              select * into vr_res from LAGER_RES_BESTAND where RES_ID=pID and REF_BESTAND=vr_bes.REF and REF_AUF_POS=pRefAufPos;

              exception
                when no_data_found then
                  vr_res := null;
                when others then
                  raise;
            end;
          end if;

          if (vr_res.REF is null) then
            if (lTraceLevel >= 5) then
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': insert Colli res');
            end if;

            insert into LAGER_RES_BESTAND
                (RES_ID,
                REF_BESTAND,
                REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, CHARGE_SOLL, FLAG_VOLLPAL, REF_LB, REF_LE, REF_LP, REF_AUFTRAG_ART_REL_LB)
              VALUES
                (pID,
                 case when nvl (rPlanArt.OPT_BES_RES, '0')='1' then vr_bes.REF else null end,
                 pRefAufPos, v_bes_menge, vr_bes.REF_AR_EINHEIT, v_bes_menge, vr_bes.REF_AR_EINHEIT, v_komm_gw, pMinMHD, vr_bes.CHARGE, v_voll_pal, v_ref_lb, vr_bes.REF_LE, vr_bes.REF_LP, vr_plan.REF_AUFTRAG_ART_REL_LB);
          else
            if (lTraceLevel >= 5) then
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': update Colli res, REF='||vr_res.REF);
            end if;

            update LAGER_RES_BESTAND set MENGE_RES=MENGE_RES+v_bes_menge, MENGE_USED=MENGE_USED+v_bes_menge where REF=vr_res.REF;
          end if;
        end;
      end if;

      if (vr_bes.REF_BES_SUM is not null) then
        --Wenn es sich beim Summebestand um die selbe Einheit wie beim Bestand handelt, also kein Colli
        if (vr_bes.REF_AR_EINHEIT = vr_bes_sum.REF_AR_EINHEIT) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bes_sum: REF='||vr_bes_sum.REF||', MENGE_RES='||vr_bes_sum.MENGE_RES||', v_bes_menge='||v_bes_menge);
          end if;

          --Reservierte Menge im Summenbestand vermerken
          update LAGER_BESTAND_SUMME_RES
            set
              MENGE_RES=NVL (MENGE_RES,0) + v_bes_menge
            where
              REF_BES_SUM=vr_bes.REF_BES_SUM
            returning MENGE_RES into v_test;

            if (v_test<0) then
              raise_application_error (-20202, 'RES-Fehler MENGE_RES='||v_test);
            end if;
        end if;
      end if;

      if (vr_bes.REF is not null and nvl (rPlanArt.OPT_BES_RES, '0')='1') then
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bes: REF='||vr_bes.REF||', MENGE_RES='||vr_bes.MENGE_RES||', v_bes_menge='||v_bes_menge);
        end if;

        --Reservierte Menge im Bestand vermerken
        update LAGER_BESTAND
          set
            MENGE_RES=NVL (MENGE_RES,0) + v_bes_menge
          where
            REF=vr_bes.REF
          returning MENGE_RES into v_test;

          if (v_test<0) then
            raise_application_error (-20203, 'RES-Fehler MENGE_RES='||v_test);
          end if;
      end if;
    end if;

    if (v_bes_menge > poRestMenge) then
      poRestMenge := 0;
    else
      poRestMenge := poRestMenge - v_bes_menge;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2,'poRestMenge='||poRestMenge);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BucheColliMenge
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BucheColliMenge (pRefBesSum    in     LAGER_BESTAND_SUMME.REF%type,
                          pRefArEinheit in     ARTIKEL_EINHEIT.REF%type,
                          pRefAufPos    in     AUFTRAG_POS.REF%type,
                          pMinMHD       in     DATE,
                          pID           in     LAGER_RES_BESTAND.RES_ID%TYPE,  --Die ID des Vorganges
                          pRefLE        in     LAGER_LE.REF%type,
                          pPalFaktor    in     ARTIKEL_EINHEIT.PAL_FAKTOR%type,
                          rPlanArt      in     AUFTRAG_ART_PLANUNG%rowtype,
                          poMenge       in out PLS_INTEGER) return number is

  v_bes_count  PLS_INTEGER;
  v_rest_menge PLS_INTEGER;

  vr_lb        LAGER_LB%rowtype;

  aktlevel     PLS_INTEGER;
  res          number;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('BucheColliMenge');

  BASE_TRACING.TraceOutput (2,'pRefBesSum   ='||pRefBesSum);
  BASE_TRACING.TraceOutput (2,'pRefArEinheit='||pRefArEinheit);
  BASE_TRACING.TraceOutput (2,'pRefAufPos   ='||pRefAufPos);
  BASE_TRACING.TraceOutput (2,'pMinMHD      ='||to_char (pMinMHD, 'dd.mm.yyyy'));
  BASE_TRACING.TraceOutput (2,'pID          ='||pID);
  BASE_TRACING.TraceOutput (2,'pRefLE       ='||pRefLE);
  BASE_TRACING.TraceOutput (2,'pPalFaktor   ='||pPalFaktor);
  BASE_TRACING.TraceOutput (2,'rPlanArt.REF ='||rPlanArt.REF);
  BASE_TRACING.TraceOutput (2,'poMenge      ='||poMenge);

  v_rest_menge := poMenge;

  --Alle Bestände durchgehen die zum Summenbestand gehören und bei denen noch nicht alles reseviert ist.
  --Sortiert nach Eingangsdatum

  if (pPalFaktor is not null) or (nvl (rPlanArt.OPT_VOLL_PAL_MEHR_MENGE, '0') = '1') then
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5,'rPlanArt.OPT_ANBRUCH_VOLL_PAL='||rPlanArt.OPT_ANBRUCH_VOLL_PAL||', OPT_VOLL_PAL_MEHR_MENGE='||rPlanArt.OPT_VOLL_PAL_MEHR_MENGE);
    end if;

    --Erst Vollplatten suchen
    for cr_bes in (select
                       bes.REF as BES_REF, bes.REF_LB, plan.REF as PLAN_REF, bes.MENGE_FREI, bes.REF_INV, bes.REF_AR, bes.REF_AR_EINHEIT
                     from
                       LAGER_BESTAND bes
                       inner join LAGER_LE le on (le.REF=bes.REF_LE)
                       left outer join LAGER_LP lelp on (lelp.REF=le.REF_LP)
                       left outer join TMP_KOMM_PLAN_LB plan on ((plan.REF_LP is null and plan.REF_LB=coalesce (le.REF_LB, lelp.REF_LB)) or (plan.REF_LP is not null and (lelp.REF=plan.REF_LP)))
                       left outer join LAGER_LB lb on (lb.REF=plan.REF_LB)
                       left outer join LAGER_NACHSCHUB_PLAN np on (np.REF_LE=bes.REF_LE)
                     where
                       --Bestand wurde bereits vereinnahmt
                       bes.STATUS='AKT' and
                       --Die Einheit muss passen
                       bes.REF_AR_EINHEIT=pRefArEinheit and
                       --Es ist noch nicht alles reserviert
                       (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0 and
                       --Der Bestand gehört zu der Summe
                       bes.REF_BES_SUM=pRefBesSum and
                       --Auf der Palette darf nichts reserviert sein
                       nvl (bes.MENGE_RES, 0)=0 and
                       --Vollpaletten-Kommissionierung muss zulässig sein
                       substr (nvl (lb.KOMM_OPT, '00000000'), PA_KOMM_PLANUNG.PLAN_VOLL_PAL, 1)='1' and
                       --Vollpaletten-Planung muss zulässig sein
                       nvl (plan.OPT_VOLL_PAL, '0')='1' and
                       --Palette ist voll oder auch Anbruchplatten als Vollpaletten genommen werden dürfen
                       ((nvl (bes.MENGE_FREI, 0) = pPalFaktor) or (nvl (rPlanArt.OPT_ANBRUCH_VOLL_PAL, '0') = '1') or (nvl (rPlanArt.OPT_VOLL_PAL_MEHR_MENGE, '0') = '1')) and
                       --LE darf nicht für einen Nachschub verplant sein
                       np.REF is null
                     order by
                     -- MHD Abfrage zuerst B.P. 26.06.23 - Ticket 20020129 ZEN
                      case when bes.MHD is not null then
                            trunc(bes.mhd)
                          else
                            null
                          end asc nulls last,
                       --Prio der zulässigen Lagerbereiche
                       plan.PRIO desc nulls last,
                       --FiFo
                          --Wenn OPT_FIFO=1 dann jetzt Fifo

                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end nulls last,
                     --  bes.EINGANG_DATUM,
                       --Als erstes die Paletten, bei den der Palettenfaktor stimmt
                       case
                         when (nvl (bes.MENGE_FREI, 0) = pPalFaktor) then
                           0
                         else
                           1
                       end asc,
                       --So sortieren, dass die geringste Abweichung entsteht
                       case
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) = 0) then
                           0
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) > 0) then
                           1
                         else
                           2
                       end asc,
                       case
                         when (nvl (plan.OPT_SELECT, 'D') = 'A') then
                           --Die grösste Menge zuerst
                           nvl (bes.MENGE_FREI, 0)
                       end asc,
                       case
                         when (nvl (plan.OPT_SELECT, 'D') = 'D') then
                           --Die kleinste Menge zuerst
                           nvl (bes.MENGE_FREI, 0)
                       end desc
                   ) loop

      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': Voll cr_bes: BES_REF='||cr_bes.BES_REF||', PLAN_REF='||cr_bes.PLAN_REF||', MENGE_FREI='||cr_bes.MENGE_FREI);
      end if;

      --Bei Vollpalette muss die freie Menge kleiner gleich der benötigten sein
      if (nvl (cr_bes.MENGE_FREI, 0) <= v_rest_menge) or (nvl (rPlanArt.OPT_VOLL_PAL_MEHR_MENGE, '0') = '1') then
        if (cr_bes.REF_INV is not null) then
          --Bestand in Inventur darf nicht kommissioniert werden
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 8, 'Ware in Inventur');
        else
          res := BucheTeilMenge (cr_bes.BES_REF, cr_bes.PLAN_REF, pRefAufPos, pMinMHD, pID, rPlanArt, true, v_rest_menge);
        end if;
      end if;

      --Suche beenden, wenn die geforderte Bestandsmenge reserviert wurde
      exit when (v_rest_menge <= 0);
    end loop;
  else
    --Wenn nach den Vollplaetten noch etwas benötigt wird
    if (v_rest_menge > 0) then
      v_bes_count := 0;

      for cr_bes in (select
                         bes.REF as BES_REF, bes.REF_LB as BES_REF_LB, plan.REF as PLAN_REF, plan.OPT_KOMM_VPE as OPT_KOMM_VPE, bes.REF_INV, bes.REF_AR as REF_AR, bes.REF_AR_EINHEIT as REF_AR_EINHEIT, plan.REF_LB as PLAN_REF_LB,
                         (select count (*) from TMP_KOMM_RES res where ((bes.REF_LE is not null and res.REF_LE=bes.REF_LE) or (bes.REF_LP is not null and res.REF_LP=bes.REF_LP))) as COUNT_RES
                       from
                         LAGER_BESTAND bes
                         left outer join LAGER_LP lp on (lp.REF=bes.REF_LP)
                         left outer join LAGER_LE le on (le.REF=bes.REF_LE)
                         left outer join LAGER_LP lelp on (lelp.REF=le.REF_LP)
                         left outer join TMP_KOMM_PLAN_LB plan on ((plan.REF_LP is null and plan.REF_LB=coalesce (le.REF_LB, lelp.REF_LB, lp.REF_LB)) or (plan.REF_LP is not null and (lelp.REF=plan.REF_LP or bes.REF_LP=plan.REF_LP)))
                       where
                         --Der Bestand gehört zu der Summe
                         bes.REF_BES_SUM=pRefBesSum and
                         --Bestand wurde bereits vereinnahmt
                         bes.STATUS='AKT' and
                         --Die Einheit muss passen
                         bes.REF_AR_EINHEIT=pRefArEinheit and
                         --Es ist noch nicht alles reserviert
                         (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0
                       order by
                         --Wenn eine LE vorgegeben ist, als erstes von dieser LE picken
                         case
                           when (pRefLE is not null and (bes.REF_LE=pRefLE)) then
                             0
                           else
                             9
                         end asc,
                          -- MHD Abfrage zuerst B.P. 26.06.23 - Ticket 20020129 ZEN
                         case when bes.MHD is not null then
                            trunc(bes.mhd)
                           else
                            null
                           end asc nulls last,
                         --Prio der zulässigen Lagerbereiche
                         plan.PRIO desc nulls last,
                         --Optimal passenden Mengen den Vorrang geben
                        -- abs (nvl (bes.MENGE_FREI, 0) - v_rest_menge) asc,
                        -- B.P. 27.07.23 - Ticket 20020758
                        case
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) = 0) then
                           0
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) > 0) then
                           1
                         else
                           2
                        end asc,
                         --Möglichst von dem Platz, auf dem schon die Reservierung für diese Planung vorliegt
                         COUNT_RES desc,
                         --Möglichst in aus einem Bestand, wenn plan.OPT_SELECT = D
                         case
                           when (nvl (plan.OPT_SELECT, 'D') = 'D') then
                             case
                               when ((nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) >= 30) then
                                 0
                               else
                                 1
                             end
                           else
                             0
                         end asc,
                         --FiFo
                            --Wenn OPT_FIFO=1 dann jetzt Fifo

                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end nulls last,
                        -- bes.EINGANG_DATUM,
                         --Die kleinste Menge zuerst
                         (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0))) loop
        v_bes_count := v_bes_count + 1;

        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': VPE cr_bes: BES_REF='||cr_bes.BES_REF||', BES_REF_LB='||cr_bes.BES_REF_LB||', PLAN_REF='||cr_bes.PLAN_REF||', PLAN_REF_LB='||cr_bes.PLAN_REF_LB||', OPT_KOMM_VPE='||cr_bes.OPT_KOMM_VPE||', COUNT_RES='||cr_bes.COUNT_RES);

        if (cr_bes.REF_INV is not null) then
          --Bestand in Inventur darf nicht kommissioniert werden
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 8, 'Ware in Inventur');
        --VPE-Kommissionierung muss zulässig sein
        elsif (nvl (cr_bes.OPT_KOMM_VPE, '0') = '1') then
          res := BucheTeilMenge (cr_bes.BES_REF, cr_bes.PLAN_REF, pRefAufPos, pMinMHD, pID, rPlanArt, false, v_rest_menge);
        elsif (cr_bes.PLAN_REF is null) then
          if (cr_bes.BES_REF_LB is null) then
            --Ware liegt im nicht konfigurierten Bereich
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 15, 'Bestand ohne Lagerbereich');
          else
            select * into vr_lb from LAGER_LB where REF=cr_bes.BES_REF_LB;

            if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
              --In dem Lagerbericht kann Nachschub gefahren werden
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 14, 'Nachschub aus Lagerbereich '||vr_lb.Name||' möglich');
            elsif (vr_lb.LB_ART in ('WE')) then
              --Ware steht noch im WE
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 2, 'Ware steht noch im WE');
            else
              --Ware liegt im nicht konfigurierten Bereich
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 16, 'Nicht freigegebener Lagerbereich '||vr_lb.Name);
            end if;
          end if;
        elsif (cr_bes.PLAN_REF_LB is not null) then
          select * into vr_lb from LAGER_LB where REF=cr_bes.PLAN_REF_LB;

          if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
            --In dem Lagerbericht kann Nachschub gefahren werden
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 14, 'Nachschub aus Lagerbereich '||vr_lb.Name||' möglich');
          else
            --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, pRefAufPos, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 11, 'VPE-Kommissionierung nicht zulässig');
          end if;
        end if;

        --Suche beenden, wenn die geforderte Bestandsmenge reserviert wurde
        exit when (v_rest_menge <= 0);
      end loop;

      if (v_bes_count = 0) then
        declare
          vr_bes_sum LAGER_BESTAND_SUMME%rowtype;
        begin
          select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=pRefBesSum;

          --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, pRefAufPos, vr_bes_sum.REF_AR, vr_bes_sum.REF_AR_EINHEIT, 10, 'Bestand noch nicht kommissionierbar');
        end;
      end if;
    end if;
  end if;

  --Wenn nicht alles reserviert werden kann, muss die Menge angepasst werden
  poMenge := poMenge - v_rest_menge;

  BASE_TRACING.TraceOutput (2,'poMenge='||poMenge);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BucheMenge
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BucheMenge (pRefBesSum  in     LAGER_BESTAND_SUMME.REF%type,
                     prAufPos    in     AUFTRAG_POS%rowtype,
                     pMinMHD     in     DATE,
                     pID         in     LAGER_RES_BESTAND.RES_ID%TYPE,  --Die ID des Vorganges
                     pPalFaktor  in     ARTIKEL_EINHEIT.PAL_FAKTOR%type,
                     rPlanArt    in     AUFTRAG_ART_PLANUNG%rowtype,
                     pPlanStep   in     integer,
                     poMenge     in out PLS_INTEGER) return number is

  v_bes_count  PLS_INTEGER;
  v_rest_menge PLS_INTEGER;

  vr_ae        ARTIKEL_EINHEIT%rowtype;
  vr_lb        LAGER_LB%rowtype;
  vr_bes_sum   VQ_LAGER_BESTAND_SUMME_PLAN%rowtype;

  v_count      PLS_INTEGER;

  aktlevel     PLS_INTEGER;
  res          number;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('BucheMenge');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pRefBesSum  ='||pRefBesSum);
    BASE_TRACING.TraceOutput (2,'prAufPos.REF='||prAufPos.REF);
    BASE_TRACING.TraceOutput (2,'pMinMHD     ='||to_char (pMinMHD, 'dd.mm.yyyy'));
    BASE_TRACING.TraceOutput (2,'pID         ='||pID);
    BASE_TRACING.TraceOutput (2,'pPalFaktor  ='||pPalFaktor);
    BASE_TRACING.TraceOutput (2,'rPlanArt.REF='||rPlanArt.REF);
    BASE_TRACING.TraceOutput (2,'pPlanStep   ='||pPlanStep);
    BASE_TRACING.TraceOutput (2,'poMenge     ='||poMenge);
  end if;

  select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=pRefBesSum;
  select * into vr_ae from ARTIKEL_EINHEIT where REF=vr_bes_sum.REF_AR_EINHEIT;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': rPlanArt : OPT_VOLL_PAL_PLAN='||rPlanArt.OPT_VOLL_PAL_PLAN);
  end if;

  v_rest_menge := poMenge;

  --Alle Bestände durchgehen die zum Summenbestand gehören und bei denen noch nicht alles reseviert ist.
  --Sortiert nach Eingangsdatum

  if (nvl (rPlanArt.OPT_VOLL_PAL_PLAN, '1') in ('1','2')) then
    if (lTraceLevel >= 3) then
      BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||': rPlanArt.OPT_ANBRUCH_VOLL_PAL='||rPlanArt.OPT_ANBRUCH_VOLL_PAL||', OPT_VOLL_PAL_MEHR_MENGE='||rPlanArt.OPT_VOLL_PAL_MEHR_MENGE);
    end if;

    loop
      v_count := 0;

      BASE_TRACING.TRACETIMESTAMP (3, '#'||$$plsql_line||': v_rest_menge='||v_rest_menge);

      --Erst Vollplatten suchen
      for cr_pal_bes in (select
                           bes.REF as BES_REF, plan.REF as PLAN_REF, bes.MENGE_FREI, bes.REF_INV, bes.REF_AR, bes.REF_AR_EINHEIT, bes.OPT_VERZOLLT, bes.CHARGE, lb.REF as REF_LB, lelp.REF as REF_LP,
                           (select count (*) from TMP_KOMM_RES res where (bes.REF_LE is not null and res.REF_LE=bes.REF_LE)) as COUNT_LE_RES,
                           (select count (*) from TMP_KOMM_RES res where ((lelp.REF is not null and res.REF_LP=lelp.REF) or (bes.REF_LE is not null and res.REF_LE=bes.REF_LE) or (bes.REF_LP is not null and res.REF_LP=bes.REF_LP))) as COUNT_LP_RES,
                           (select count (*) from TMP_KOMM_RES res where res.REF_LB=lb.REF) as COUNT_LB_RES
                         from
                           LAGER_BESTAND bes
                           inner join LAGER_LE le on (le.REF=bes.REF_LE)
                           left outer join LAGER_LP lelp on (lelp.REF=le.REF_LP)
                           left outer join TMP_KOMM_PLAN_LB plan on ((plan.REF_LP is null and plan.REF_LB=coalesce (le.REF_LB, lelp.REF_LB)) or (plan.REF_LP is not null and (lelp.REF=plan.REF_LP)))
                           left outer join LAGER_LB lb on (lb.REF=plan.REF_LB)
                           left outer join LAGER_NACHSCHUB_PLAN np on (np.REF_LE=bes.REF_LE)
                         where
                           --Der Bestand gehört zu der Summe
                           bes.REF_BES_SUM=pRefBesSum and
                           --Bestand wurde bereits vereinnahmt
                           bes.STATUS='AKT' and
                           --Es ist noch nicht alles reserviert
                           (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0 and
                           --Auf der Palette darf nichts reserviert sein
                           nvl (bes.MENGE_RES, 0)=0 and
                           --Vollpaletten-Kommissionierung muss zulässig sein
                           substr (nvl (lb.KOMM_OPT, '00000000'), PA_KOMM_PLANUNG.PLAN_VOLL_PAL, 1)='1' and
                           --Vollpaletten-Planung muss zulässig sein
                           nvl (plan.OPT_VOLL_PAL, '0')='1' and
                           --Palette ist voll oder auch Anbruchplatten als Vollpaletten genommen werden dürfen
                           ((pPalFaktor is not null and (nvl (bes.MENGE_FREI, 0) = pPalFaktor)) or (nvl (rPlanArt.OPT_ANBRUCH_VOLL_PAL, '0') = '1') or (nvl (rPlanArt.OPT_VOLL_PAL_MEHR_MENGE, '0') = '1')) and
                           --LE darf nicht für einenn Nachschub verplant sein
                           np.REF is null
                         order by
                          -- MHD Abfrage zuerst B.P. 26.06.23 - Ticket 20020129 ZEN
                           case when bes.MHD is not null then
                            trunc(bes.mhd)
                           else
                            null
                           end asc nulls last,
                           --Prio der zulässigen Lagerbereiche
                           plan.PRIO desc nulls last,
                           --Möglichst von dem Bereich, auf dem schon die Reservierung für diese Planung vorliegt
                           COUNT_LB_RES desc,
                           --Möglichst von der LE, auf dem schon die Reservierung für diese Planung vorliegt
                           COUNT_LE_RES desc,
                           --Möglichst von dem Platz, auf dem schon die Reservierung für diese Planung vorliegt
                           COUNT_LP_RES desc,
                           --Als erstes die Paletten, bei den der Palettenfaktor stimmt
                           case
                             when pPalFaktor is null then
                               0
                             when (nvl (bes.MENGE_FREI, 0) = pPalFaktor) then
                               0
                             else
                               1
                           end asc,
                           --Wenn OPT_FIFO=1 dann jetzt Fifo

                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end nulls last,
                           --So sortieren, dass die geringste Abweichung entsteht
                           case
                             when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) = 0) then
                               0
                             when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) > 0) then
                               1
                             else
                               10000 / (nvl (bes.MENGE_FREI, 0))
                           end asc,
                           --FiFo
                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end nulls last,
                           case
                             when (nvl (substr (plan.OPT_SELECT, 1, 1), 'D') = 'A') then
                               --Die grösste Menge zuerst
                               nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)
                           end asc,
                           case
                             when (nvl (substr (plan.OPT_SELECT, 1, 1), 'D') = 'D') then
                               --Die kleinste Menge zuerst
                               nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)
                           end desc
                     ) loop

        if (lTraceLevel >= 5) then
          BASE_TRACING.TRACETIMESTAMP (5, '#'||$$plsql_line||': Voll cr_pal_bes: BES_REF='||cr_pal_bes.BES_REF||', REF_LB='||cr_pal_bes.REF_LB||', REF_LP='||cr_pal_bes.REF_LP||', COUNT_LB_RES='||cr_pal_bes.COUNT_LB_RES||', COUNT_LP_RES='||cr_pal_bes.COUNT_LP_RES||', PLAN_REF='||cr_pal_bes.PLAN_REF||', MENGE_FREI='||cr_pal_bes.MENGE_FREI);
        end if;

        --Bei Vollpalette muss die freie Menge kleiner gleich der benötigten sein
        if (nvl (cr_pal_bes.MENGE_FREI, 0) <= v_rest_menge) or (nvl (rPlanArt.OPT_VOLL_PAL_MEHR_MENGE, '0') = '1') then
          if (cr_pal_bes.REF_INV is not null) then
            --Bestand in Inventur darf nicht kommissioniert werden
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_pal_bes.REF_AR, cr_pal_bes.REF_AR_EINHEIT, 8, 'Ware in Inventur');
          elsif (nvl (cr_pal_bes.OPT_VERZOLLT, '1') = '0') then
            --Unverzollter Bestand darf nicht kommissioniert werden
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_pal_bes.REF_AR, cr_pal_bes.REF_AR_EINHEIT, 7, 'Ware ist noch nicht verzollt');
          elsif (prAufPos.CHARGE is not null and nvl (cr_pal_bes.CHARGE, '#') <> prAufPos.CHARGE) then
            --Unverzollter Bestand darf nicht kommissioniert werden
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_pal_bes.REF_AR, cr_pal_bes.REF_AR_EINHEIT, 5, 'Charge passt nicht');
          else
            declare
              v_menge integer := v_rest_menge;
            begin
              res := BucheTeilMenge (cr_pal_bes.BES_REF, cr_pal_bes.PLAN_REF, prAufPos.REF, pMinMHD, pID, rPlanArt, true, v_rest_menge);

              if (nvl (v_menge, -1) <> nvl (v_rest_menge, -1)) then
                v_count := v_count + 1;

                exit;
              end if;
            end;
          end if;
        end if;
      end loop;

      exit when (res <> 0);

      --Suche beenden, wenn die geforderte Bestandsmenge reserviert wurde
      exit when (v_rest_menge <= 0);

      --Suche beenden, wenn keine weiter Vollpalette gefunden wurde
      exit when (v_count = 0);
    end loop;
  end if;

  --pPlanStep = 1 nur Vollpaletten, pPlanStep = auch VE zulässig
  if (pPlanStep = 2) and (nvl (rPlanArt.OPT_VOLL_PAL_PLAN, '1') in ('0','1')) then
    --Wenn nach den Vollplaetten noch etwas benötigt wird
    if (v_rest_menge > 0) then
      v_bes_count := 0;

      for cr_vpe_bes in (select
                           bes.REF as BES_REF, bes.REF_LB as BES_REF_LB, plan.REF as PLAN_REF, plan.OPT_SELECT, plan.OPT_KOMM_VPE as OPT_KOMM_VPE, bes.REF_INV,
                           bes.REF_AR as REF_AR, bes.REF_AR_EINHEIT as REF_AR_EINHEIT, plan.REF_LB as PLAN_REF_LB,
                           bes.OPT_VERZOLLT, bes.CHARGE, bes.MENGE_FREI, plan.OPT_KOMM_ITEM, plan.MIN_BESTAND,lb.KOMM_OPT,
                           null as COUNT_LP_RES,
                           --(select count (*) from TMP_KOMM_RES res where ((bes.REF_LE is not null and res.REF_LE=bes.REF_LE) or (bes.REF_LP is not null and res.REF_LP=bes.REF_LP))) as COUNT_LP_RES,
                           null as COUNT_LB_RES,
                           --(select count (*) from TMP_KOMM_RES res where res.REF_LB=plan.REF_LB) as COUNT_LB_RES,
                           null as COUNT_LB_PICK_RES,
                           --(select count (*) from LAGER_RES_BESTAND res where res.REF_LB=lelp.REF_LB) as COUNT_LB_PICK_RES,
                           null as COUNT_BES_PICK_RES
                           --(select count (*) from LAGER_RES_BESTAND res where res.REF_BESTAND=bes.REF) as COUNT_BES_PICK_RES
                         from
                           LAGER_BESTAND bes
                           left outer join LAGER_LE le on (le.REF=bes.REF_LE)
                           left outer join LAGER_LP lelp on ((le.REF_LP is not null and lelp.REF=le.REF_LP) or (bes.REF_LP is not null and lelp.REF=bes.REF_LP))
                           left outer join TMP_KOMM_PLAN_LB plan on ((plan.REF_LP is null and plan.REF_LB=lelp.REF_LB) or (plan.REF_LP is not null and plan.REF_LP=lelp.REF))
                           left outer join LAGER_LB lb on (lb.REF=plan.REF_LB)
                         where
                           --Der Bestand gehört zu der Summe
                           bes.REF_BES_SUM=pRefBesSum and
                           --Bestand wurde bereits vereinnahmt
                           bes.STATUS='AKT' and
                           --Es ist noch nicht alles reserviert
                           (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0
                         order by
                          -- MHD Abfrage zuerst B.P. 26.06.23 - Ticket 20020129 ZEN
                           case when bes.MHD is not null then
                            trunc(bes.mhd)
                           else
                            null
                           end asc nulls last,
                           --Prio der zulässigen Lagerbereiche
                           plan.PRIO desc nulls last,
                           --Möglichst von dem Bereich, auf dem schon die Reservierung für diese Planung vorliegt
                           COUNT_LB_RES desc nulls last,
                           --Möglichst von dem Platz, auf dem schon die Reservierung für diese Planung vorliegt
                           /*
                           case
                             when (nvl (substr (lb.KOMM_OPT, PA_LB.KOMM_OPT_LP_BALANCE, 1), '0') = '0') then
                               COUNT_LP_RES
                             else
                               null
                           end
                           */
                           COUNT_LP_RES desc nulls last,
                           --Wenn OPT_FIFO=1 dann jetzt Fifo
                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end asc nulls last,
                           --Den Platz mit den wenigsten geplanten Picks auswählen
                           COUNT_LB_PICK_RES asc nulls first,
                            --Je genauer die Menge passt, desto besser
                           COUNT_BES_PICK_RES asc nulls first,
                           case
                             when (nvl (substr (plan.OPT_SELECT, 2,1), ' ') = '=') then
                               case when ((nvl (bes.MENGE_FREI - nvl (bes.MENGE_RES, 0), 0) - v_rest_menge) = 0) then
                                 0
                               else
                                 null
                               end
                             when (nvl (substr (plan.OPT_SELECT, 2, 1), ' ') = '>') then
                               case when ((nvl (bes.MENGE_FREI - nvl (bes.MENGE_RES, 0), 0) - v_rest_menge) > 0) then
                                 nvl (bes.MENGE_FREI - nvl (bes.MENGE_RES, 0), 0) - v_rest_menge
                               else
                                 null
                               end
                             else
                               null
                           end asc nulls last,
                           --Die grösste Menge zuerst wenn plan.OPT_SELECT='D'
                           case
                             when (nvl (substr (plan.OPT_SELECT, 1, 1), 'D') = 'D') then
                               bes.MENGE_FREI
                             else
                               null
                           end desc nulls last,
                           --Die kleinste Menge zuerst wenn plan.OPT_SELECT='A'
                           case
                             when (nvl (substr (plan.OPT_SELECT, 1, 1), 'D') = 'A') then
                               bes.MENGE_FREI
                             else
                               null
                           end asc nulls last,

                           --FiFo
                           case
                             when (nvl (rPlanArt.OPT_FIFO, '1') = '1') then
                               trunc (bes.EINGANG_DATUM)
                             else
                               null
                           end asc nulls last
                        ) loop
        v_bes_count := v_bes_count + 1;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TRACETIMESTAMP (5, '#'||$$plsql_line||': cr_vpe_bes: BES_REF='||cr_vpe_bes.BES_REF||', MENGE_FREI='||cr_vpe_bes.MENGE_FREI||', BES_REF_LB='||cr_vpe_bes.BES_REF_LB||', PLAN_REF='||cr_vpe_bes.PLAN_REF||', PLAN_REF_LB='||cr_vpe_bes.PLAN_REF_LB||', OPT_SELECT='||cr_vpe_bes.OPT_SELECT||', OPT_KOMM_VPE='||cr_vpe_bes.OPT_KOMM_VPE||', COUNT_LP_RES='||cr_vpe_bes.COUNT_LP_RES||', COUNT_BES_PICK_RES='||cr_vpe_bes.COUNT_BES_PICK_RES);
        end if;

        if (cr_vpe_bes.REF_INV is not null) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 8');
          end if;

          --Bestand in Inventur darf nicht kommissioniert werden
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 8, 'Ware in Inventur');
        elsif (nvl (cr_vpe_bes.OPT_VERZOLLT, '1') = '0') then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 7');
          end if;

          --Unverzollter Bestand darf nicht kommissioniert werden
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 7, 'Ware ist noch nicht verzollt');
        elsif (cr_vpe_bes.MIN_BESTAND is not null and cr_vpe_bes.MENGE_FREI < cr_vpe_bes.MIN_BESTAND) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 6');
          end if;

          --Charge passt nicht zur geforderten
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 5, 'Frei Mnege zu gering');
        elsif (prAufPos.CHARGE is not null and nvl (cr_vpe_bes.CHARGE, '#') <> prAufPos.CHARGE) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 5');
          end if;

          --Charge passt nicht zur geforderten
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 5, 'Charge passt nicht');
        elsif (nvl (cr_vpe_bes.OPT_KOMM_ITEM, '0') = '1') and (vr_ae.STUECK_KENNZEICHEN = '1') then
          --VPE-Kommissionierung muss zulässig sein
          res := BucheTeilMenge (cr_vpe_bes.BES_REF, cr_vpe_bes.PLAN_REF, prAufPos.REF, pMinMHD, pID, rPlanArt, false, v_rest_menge);
        elsif (nvl (cr_vpe_bes.OPT_KOMM_VPE, '0') = '1') then
          --VPE-Kommissionierung muss zulässig sein
          res := BucheTeilMenge (cr_vpe_bes.BES_REF, cr_vpe_bes.PLAN_REF, prAufPos.REF, pMinMHD, pID, rPlanArt, false, v_rest_menge);
        else
          if (cr_vpe_bes.PLAN_REF is null) then
            if (cr_vpe_bes.BES_REF_LB is null) then
              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 15');
              end if;

              --Ware liegt im nicht konfigurierten Bereich
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 15, 'Bestand ohne Lagerbereich');
            else
              select * into vr_lb from LAGER_LB where REF=cr_vpe_bes.BES_REF_LB;

              if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 14');
                end if;

                --In dem Lagerbericht kann Nachschub gefahren werden
                insert into TMP_BES_RES_FEHLER
                    (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                  values
                    (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 14, substr ('Nachschub aus Lagerbereich '||vr_lb.Name||' möglich', 1, 64));
              elsif (vr_lb.LB_ART in ('WE')) then
                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 2');
                end if;

                --Ware steht noch im WE
                insert into TMP_BES_RES_FEHLER
                    (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                  values
                    (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 2, 'Ware steht noch im WE');
              elsif (vr_lb.KOMM_ART = 'EXT') then
                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 19');
                end if;

                --Ware liegt in einem externen Lager
                insert into TMP_BES_RES_FEHLER
                    (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                  values
                    (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 19, substr ('Ware im externen Lagerbereich '||vr_lb.Name, 1, 64));
              else
                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 16');
                end if;

                --Ware liegt im nicht konfigurierten Bereich
                insert into TMP_BES_RES_FEHLER
                    (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                  values
                    (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 16, substr ('Nicht freigegebener Lagerbereich '||vr_lb.Name, 1, 64));
              end if;
            end if;
          elsif (cr_vpe_bes.PLAN_REF_LB is not null) then
            select * into vr_lb from LAGER_LB where REF=cr_vpe_bes.PLAN_REF_LB;

            if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 14');
              end if;

              --In dem Lagerbericht kann Nachschub gefahren werden
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 14, substr ('Nachschub aus Lagerbereich '||vr_lb.Name||' möglich', 1, 64));
            else
              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 11');
              end if;

              --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
              insert into TMP_BES_RES_FEHLER
                  (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
                values
                  (pID, prAufPos.REF, cr_vpe_bes.REF_AR, cr_vpe_bes.REF_AR_EINHEIT, 11, 'VPE-Kommissionierung nicht zulässig');
            end if;
          end if;
        end if;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, 'v_rest_menge='||v_rest_menge);
        end if;

        --Suche beenden, wenn die geforderte Bestandsmenge reserviert wurde
        exit when (v_rest_menge <= 0);
      end loop;

      if (v_bes_count = 0) then
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 10');
        end if;

        --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
        insert into TMP_BES_RES_FEHLER
            (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
          values
            (pID, prAufPos.REF, vr_bes_sum.REF_AR, vr_bes_sum.REF_AR_EINHEIT, 10, 'Bestand noch nicht kommissionierbar');
      end if;

      if (nvl (vr_bes_sum.MENGE_RES_NACH, 0) > 0) then
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 17');
        end if;

        --Es ist noch Nachschub geplant
        insert into TMP_BES_RES_FEHLER
            (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
          values
            (pID, prAufPos.REF, vr_bes_sum.REF_AR, vr_bes_sum.REF_AR_EINHEIT, 17, 'Nachschub angelegt');
      else
        select count (np.REF) into v_count from LAGER_NACHSCHUB_POS np, LAGER_NACHSCHUB n where n.REF=np.REF_NACHSCHUB and n.REF_LAGER=vr_bes_sum.REF_LAGER and n.STATUS<>'ABG' and np.STATUS<>'ABG' and np.REF_AR_EINHEIT=vr_bes_sum.REF_AR_EINHEIT;

        if (v_count > 0) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': 18');
          end if;

          --Nachschub läuft noch
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, vr_bes_sum.REF_AR, vr_bes_sum.REF_AR_EINHEIT, 18, 'Nachschub läuft noch');
        end if;
      end if;
    end if;
  end if;

  --Wenn nicht alles reserviert werden kann, muss die Menge angepasst werden
  poMenge := poMenge - v_rest_menge;

  BASE_TRACING.TraceOutput (2,'poMenge='||poMenge);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BucheTeilMenge
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BucheInhaltTeilMenge (pRefBes       in     LAGER_BESTAND.REF%type,
                               pRefPlan      in     TMP_KOMM_PLAN_LB.REF%type,
                               pRefAufPos    in     AUFTRAG_POS.REF%type,
                               pMinMHD       in     DATE,
                               pID           in     LAGER_RES_BESTAND.RES_ID%TYPE,  --Die ID des Vorganges
                               pRefAEUsed    in     ARTIKEL_EINHEIT.REF%type,
                               pInhaltAnz    in     PLS_INTEGER,
                               poRestUsed    in out PLS_INTEGER,
                               poRestMenge   in out PLS_INTEGER) return number is

  vr_lb       LAGER_LB%rowtype;
  vr_bes      LAGER_BESTAND%rowtype;
  vr_plan     TMP_KOMM_PLAN_LB%rowtype := null;
  vr_sum_res  LAGER_RES_BESTAND%rowtype;

  vr_auf_pos  AUFTRAG_POS%rowtype := null;

  v_ref_lp    LAGER_LP.REF%type := null;
  v_ref_lb    LAGER_LB.REF%type := null;

  v_bes_menge PLS_INTEGER;
  v_bes_used  PLS_INTEGER;
  v_komm_gw   PLS_INTEGER;

  v_test      PLS_INTEGER;
  aktlevel    PLS_INTEGER;
  res         number;
begin
  res := 0;

  vr_lb := null;

  aktlevel := BASE_TRACING.ENTERPROC ('BucheInhaltTeilMenge');

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (2,'pRefBes      ='||pRefBes);
    BASE_TRACING.TraceOutput (2,'pRefPlan     ='||pRefPlan);
    BASE_TRACING.TraceOutput (2,'pRefAufPos   ='||pRefAufPos);
    BASE_TRACING.TraceOutput (2,'pMinMHD      ='||to_char (pMinMHD, 'dd.mm.yyyy'));
    BASE_TRACING.TraceOutput (2,'pID          ='||pID);
    BASE_TRACING.TraceOutput (2,'pRefAEUsed   ='||pRefAEUsed);
    BASE_TRACING.TraceOutput (2,'pInhaltAnz   ='||pInhaltAnz);
    BASE_TRACING.TraceOutput (2,'poRestUsed   ='||poRestUsed);
    BASE_TRACING.TraceOutput (2,'poRestMenge  ='||poRestMenge);
  end if;

  select * into vr_bes from LAGER_BESTAND where REF=pRefBes;

  if (pRefPlan is not null) then
    select * into vr_plan from TMP_KOMM_PLAN_LB where REF=pRefPlan;
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_bes: REF='||vr_bes.REF||', REF_LP='||vr_bes.REF_LP||', REF_LE='||vr_bes.REF_LE||', REF_LB='||vr_bes.REF_LB||', vr_plan.REF_LB='||vr_plan.REF_LB);
  end if;

  --Der Bestand muss bereits eingelagert und in einem zulässigen Bereich liegen
  if (vr_bes.REF_LP is not null) then
    --Bereich des Lagerplatzes
    select * into vr_lb from LAGER_LB where REF=(select REF_LB from LAGER_LP where REF=vr_bes.REF_LP);
  elsif (vr_bes.REF_LE is not null) then
    declare
      vr_le LAGER_LE%ROWTYPE;
    begin
      select * into vr_le from LAGER_LE where REF=vr_bes.REF_LE;

      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_le: REF_LP='||vr_le.REF_LP||', REF_LB='||vr_le.REF_LB);
      end if;

      if (vr_le.REF_LP is not null) then
        --Bereich des Lagerplatzes des LEs
        select * into vr_lb from LAGER_LB where REF=(select REF_LB from LAGER_LP where REF=vr_le.REF_LP);
      elsif (vr_le.REF_LB is not null) then
        --Bereich des LEs
        select * into vr_lb from LAGER_LB where REF=vr_le.REF_LB;
      end if;
    end;
  elsif (vr_bes.REF_LB is not null) then
    --Bereich des Bestandes
    select * into vr_lb from LAGER_LB where REF=vr_bes.REF_LB;
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_lb: REF='||vr_lb.REF||', LB_ART='||vr_lb.LB_ART);
  end if;

  --Prüfung, ob der Bestand auch in einem zulässigen Lagerbereich liegt
  if vr_lb.REF is null then
    --Kein Lagerbericht geht gar nicht
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 1, 'Ware noch nicht eingelagert');
  elsif (vr_lb.LB_ART in ('WE') and vr_plan.REF_LB is null) then
    --Noch nicht eingelagert
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 2, 'Ware steht noch im WE');
  elsif (vr_lb.LB_ART in ('WA') and vr_plan.REF_LB is null) then
    --Noch nicht eingelagert
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 3, 'Ware noch nicht rückgelagert');
  elsif (vr_plan.REF_LB is null) then
    --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
    insert into TMP_BES_RES_FEHLER
        (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
      values
        (pID, pRefAufPos, vr_bes.REF_AR, vr_bes.REF_AR_EINHEIT, 6, 'Ware noch nicht im Zugriff (LB)');
  elsif (poRestMenge > nvl (vr_plan.MIN_VPE, 0)) and (poRestMenge <= nvl (vr_plan.MAX_VPE, 999999999)) then
    --Die Abgrenzung der Mengen für den Lagerbereich passt auch
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': poRestMenge='||poRestMenge||', vr_bes.REF='||vr_bes.REF||', vr_bes.MENGE_FREI='||vr_bes.MENGE_FREI||', vr_bes.MENGE_RES='||vr_bes.MENGE_RES);
    end if;

    --Zu reservierende Menge bestimmen
    if (poRestMenge > (nvl (vr_bes.MENGE_FREI, 0) - nvl (vr_bes.MENGE_RES, 0))) then
      v_bes_menge := (nvl (vr_bes.MENGE_FREI, 0) - nvl (vr_bes.MENGE_RES, 0));
      v_bes_used  := v_bes_menge * pInhaltAnz;
    else
      v_bes_menge := poRestMenge;
      v_bes_used  := poRestUsed;
    end if;

    v_komm_gw := (vr_bes.NETTO_GEWICHT / vr_bes.MENGE_FREI) * v_bes_menge;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_bes_menge='||v_bes_menge||', v_bes_used='||v_bes_used);
    end if;

    if (pRefAufPos is not null) then
      select * into vr_auf_pos from AUFTRAG_POS where REF=pRefAufPos;
    end if;

    if (vr_bes.REF_LP is not null) then
      select lp.REF, lp.REF_LB into v_ref_lp, v_ref_lb from LAGER_LP lp where lp.REF=vr_bes.REF_LP;
    elsif (vr_bes.REF_LE is not null) then
      select lp.REF, lp.REF_LB into v_ref_lp, v_ref_lb from LAGER_LE le left outer join LAGER_LP lp on (lp.REF=le.REF_LP) where le.REF=vr_bes.REF_LE;
    end if;

    if (pID is null) then
      null;
    else
      begin
        select * into vr_sum_res from LAGER_RES_BESTAND where RES_ID is null and REF_BES_SUM is not null and REF_BES_SUM=vr_bes.REF for update;

        exception
          when no_data_found then
            vr_sum_res := null;
          when others then
            raise;
      end;

      if (vr_sum_res.REF is null) then
        insert into LAGER_RES_BESTAND
            (RES_ID, REF_AUF_POS, REF_BES_SUM, REF_BESTAND, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, REF_LB, REF_LE, REF_LP)
          VALUES
            (null, null, vr_bes.REF_BES_SUM, vr_bes.REF, v_bes_menge, vr_bes.REF_AR_EINHEIT, v_bes_used, pRefAEUsed, v_komm_gw, pMinMHD, v_ref_lb, vr_bes.REF_LE, vr_bes.REF_LP)
          returning REF into vr_sum_res.REF;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': insert vr_sum_res.REF='||vr_sum_res.REF);
        end if;
      else
        update LAGER_RES_BESTAND set MENGE_RES=MENGE_RES + v_bes_menge, MENGE_USED=v_bes_used where REF=vr_sum_res.REF;
      end if;

      insert into LAGER_RES_BESTAND
          (REF_SAMMEL_RES, RES_ID, REF_BES_SUM, REF_BESTAND, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, FLAG_VOLLPAL, REF_LB, REF_LE, REF_LP)
        VALUES
          (vr_sum_res.REF, pID, vr_bes.REF_BES_SUM, vr_bes.REF, pRefAufPos, null, vr_bes.REF_AR_EINHEIT, v_bes_used, pRefAEUsed, v_komm_gw, pMinMHD, '0', v_ref_lb, vr_bes.REF_LE, vr_bes.REF_LP);

      insert into TMP_KOMM_RES (RES_ID, REF_LB, REF_LP, REF_LE, REF_BAT_CONFIG) values (pID, v_ref_lb, v_ref_lp, vr_bes.REF_LE, vr_plan.REF_BAT_CONFIG);

      if (vr_bes.REF_BES_SUM is not null) then
        --Reservierte Menge im Summenbestand vermerken
        update LAGER_BESTAND_SUMME_RES
          set
            MENGE_RES=NVL (MENGE_RES,0) + v_bes_menge
          where
            REF_BES_SUM=vr_bes.REF_BES_SUM
          returning MENGE_RES into v_test;

          if (v_test<0) then
            raise_application_error (-20202, 'RES-Fehler MENGE_RES='||v_test);
          end if;
      end if;

      if (vr_bes.REF is not null) then
        --Reservierte Menge im Bestand vermerken
        update LAGER_BESTAND
          set
            MENGE_RES=NVL (MENGE_RES,0) + v_bes_menge
          where
            REF=vr_bes.REF
          returning MENGE_RES into v_test;

          if (v_test<0) then
            raise_application_error (-20203, 'RES-Fehler MENGE_RES='||v_test);
          end if;
      end if;
    end if;

    poRestUsed  := poRestUsed - v_bes_used;
    poRestMenge := poRestMenge - v_bes_menge;
  end if;

  BASE_TRACING.TraceOutput (2,'poRestUsed='||poRestUsed||', poRestMenge='||poRestMenge);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BucheInhaltMenge
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BucheInhaltMenge (pRefBesSum    in     LAGER_BESTAND_SUMME.REF%type,
                           prAufPos      in     AUFTRAG_POS%rowtype,
                           pMinMHD       in     DATE,
                           pID           in     LAGER_RES_BESTAND.RES_ID%TYPE,  --Die ID des Vorganges
                           pRefAEUsed    in     ARTIKEL_EINHEIT.REF%type,
                           pInhaltAnz    in     PLS_INTEGER,
                           poMengeRes    in out PLS_INTEGER,
                           poMengeUsed   in out PLS_INTEGER) return number is

  vr_lb        LAGER_LB%rowtype;

  v_rest_menge PLS_INTEGER;
  v_rest_used  PLS_INTEGER;
  v_bes_count  PLS_INTEGER;

  aktlevel     PLS_INTEGER;

  res          number;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('BucheInhaltMenge');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pRefBesSum    ='||pRefBesSum);
    BASE_TRACING.TraceOutput (2,'pRefAufPos.REF='||prAufPos.REF);
    BASE_TRACING.TraceOutput (2,'pMinMHD       ='||to_char (pMinMHD, 'dd.mm.yyyy'));
    BASE_TRACING.TraceOutput (2,'pID           ='||pID);
    BASE_TRACING.TraceOutput (2,'pRefAEUsed    ='||pRefAEUsed);
    BASE_TRACING.TraceOutput (2,'pInhaltAnz    ='||pInhaltAnz);
    BASE_TRACING.TraceOutput (2,'poMengeRes    ='||poMengeRes);
    BASE_TRACING.TraceOutput (2,'poMengeUsed   ='||poMengeUsed);
  end if;

  v_rest_used  := poMengeUsed;
  v_rest_menge := poMengeRes;

  v_bes_count := 0;

  for cr_bes in (select
                     bes.REF as BES_REF, plan.REF as PLAN_REF, bes.REF_INV, bes.REF_AR, bes.REF_AR_EINHEIT, plan.OPT_KOMM_ITEM, bes.OPT_VERZOLLT, bes.CHARGE, bes.REF_LB as BES_REF_LB, plan.REF_LB as PLAN_REF_LB
                   from
                     LAGER_BESTAND bes
                     left outer join LAGER_LP lp on (lp.REF=bes.REF_LP)
                     left outer join LAGER_LE le on (le.REF=bes.REF_LE)
                     left outer join LAGER_LP lelp on (lelp.REF=bes.REF_LP)
                     left outer join TMP_KOMM_PLAN_LB plan on (plan.REF_LB=coalesce (lp.REF_LB, le.REF_LB, lelp.REF_LB))
                   where
                     --Bestand wurde bereits vereinnahmt
                     bes.STATUS='AKT' and
                     --Es ist noch nicht alles reserviert
                     (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0 and
                     --Der Bestand gehört zu der Summe
                     bes.REF_BES_SUM=pRefBesSum
                   order by
                    -- MHD Abfrage zuerst B.P. 26.06.23 - Ticket 20020129 ZEN
                   case when bes.MHD is not null then
                        trunc(bes.mhd)
                     else
                         null
                     end asc nulls last,
                     --Prio der zulässigen Lagerbereiche
                     plan.PRIO desc nulls last,
                     -- B.P. 27.07.23 - Ticket 20020758
                        case
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) = 0) then
                           0
                         when ((nvl (bes.MENGE_FREI, 0) - v_rest_menge) > 0) then
                           1
                         else
                           2
                        end asc,
                     --Die grösste Menge zuerst wenn plan.OPT_SELECT='D'
                     case
                       when (nvl (plan.OPT_SELECT, 'D') = 'D') then
                         bes.MENGE_FREI
                       else
                         null
                     end desc nulls last,
                     --Die kleinste Menge zuerst wenn plan.OPT_SELECT='A'
                     case
                       when (nvl (plan.OPT_SELECT, 'D') = 'A') then
                         bes.MENGE_FREI
                       else
                         null
                     end asc nulls last,
                     --Je genauer die Menge passt, desto besser
                     abs (nvl (bes.MENGE_FREI, 0) - v_rest_menge) asc,
                     --FiFo

                     trunc (bes.EINGANG_DATUM)
                  ) loop

    v_bes_count := v_bes_count + 1;

    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> cr_bes: BES_REF='||cr_bes.BES_REF||', PLAN_REF='||cr_bes.PLAN_REF||', OPT_KOMM_ITEM='||cr_bes.OPT_KOMM_ITEM);

    if (cr_bes.REF_INV is not null) then
      --Bestand in Inventur darf nicht kommissioniert werden
      insert into TMP_BES_RES_FEHLER
          (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
        values
          (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 8, 'Ware in Inventur');
    elsif (nvl (cr_bes.OPT_VERZOLLT, '1') = '0') then
      --Unverzollter Bestand darf nicht kommissioniert werden
      insert into TMP_BES_RES_FEHLER
          (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
        values
          (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 7, 'Ware ist noch nicht verzollt');
    elsif (prAufPos.CHARGE is not null and nvl (cr_bes.CHARGE, '#') <> prAufPos.CHARGE) then
      --Unverzollter Bestand darf nicht kommissioniert werden
      insert into TMP_BES_RES_FEHLER
          (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
        values
          (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 5, 'Charge passt nicht');
    elsif (cr_bes.PLAN_REF is not null and nvl (cr_bes.OPT_KOMM_ITEM, '0') = '1') then
    --VPE-Kommissionierung muss zulässig sein
      res := BucheInhaltTeilMenge (cr_bes.BES_REF, cr_bes.PLAN_REF, prAufPos.REF, pMinMHD, pID, pRefAEUsed, pInhaltAnz, v_rest_used, v_rest_menge);
    else
      if (cr_bes.PLAN_REF is null) then
        if (cr_bes.BES_REF_LB is null) then
          --Ware liegt im nicht konfigurierten Bereich
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 15, 'Bestand ohne Lagerbereich');
        else
          select * into vr_lb from LAGER_LB where REF=cr_bes.BES_REF_LB;

          if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
            --In dem Lagerbericht kann Nachschub gefahren werden
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 14, 'Nachschub aus Lagerbereich '||vr_lb.Name||' möglich');
          elsif (vr_lb.LB_ART in ('WE')) then
            --Ware steht noch im WE
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 2, 'Ware steht noch im WE');
          else
            --Ware liegt im nicht konfigurierten Bereich
            insert into TMP_BES_RES_FEHLER
                (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
              values
                (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 16, 'Nicht freigegebener Lagerbereich '||vr_lb.Name);
          end if;
        end if;
      elsif (cr_bes.PLAN_REF_LB is not null) then
        select * into vr_lb from LAGER_LB where REF=cr_bes.PLAN_REF_LB;

        if (nvl (vr_lb.USE_FOR_VORRAT, '0') = '1') then
          --In dem Lagerbericht kann Nachschub gefahren werden
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 14, 'Nachschub aus Lagerbereich '||vr_lb.Name||' möglich');
        else
          --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, prAufPos.REF, cr_bes.REF_AR, cr_bes.REF_AR_EINHEIT, 11, 'VPE-Kommissionierung nicht zulässig');
        end if;
      end if;
    end if;

    --Suche beenden, wenn die geforderte Bestandsmenge reserviert wurde
    exit when (v_rest_menge <= 0);
  end loop;

  if (v_bes_count = 0) then
    declare
      vr_bes_sum LAGER_BESTAND_SUMME%rowtype;
    begin
      select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=pRefBesSum;

      --In dem Lagerbericht darf nicht kommissioniert werden, z. B. Nachschub usw.
      insert into TMP_BES_RES_FEHLER
          (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
        values
          (pID, prAufPos.REF, vr_bes_sum.REF_AR, vr_bes_sum.REF_AR_EINHEIT, 10, 'Bestand noch nicht kommissionierbar');
    end;
  end if;

  --Wenn nicht alles reserviert werden kann, muss die Menge angepasst werden
  poMengeRes  := poMengeRes - v_rest_menge;
  poMengeUsed := poMengeUsed - v_rest_used;

  BASE_TRACING.TraceOutput (2,'poMengeRes='||poMengeRes||', poMengeUsed='||poMengeUsed);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BUCHE_RES_BESTAND (prAufPos        in AUFTRAG_POS%ROWTYPE,             --Für diese Auftragspos. wird reserviert
                            vr_auf_pos_ae   in ARTIKEL_EINHEIT%ROWTYPE,         --Diese Einheit wird benötigt
                            vr_ar_einheit   in ARTIKEL_EINHEIT%ROWTYPE,         --Diese Einheit wird reserviert
                            vr_bes_sum      in VQ_LAGER_BESTAND_SUMME_PLAN%ROWTYPE,     --Aus diesem Bestand wird reserviert
                            pMinMHD         in LAGER_RES_BESTAND.MHD_MIN%TYPE,  --Min. MHD laut Auftrag
                            pStockRequire   in integer,                         --Diese Menge soll auf jeden Fall im Lager überbleiben
                            v_benoetigt     in out INTEGER,                     --Die Menge wird benötigt (vr_auf_pos_ae)
                            v_vpe_benoetigt in out INTEGER,                     --Diese Menge soll serviert werden (vr_auf_pos_ae)
                            pID             in LAGER_RES_BESTAND.RES_ID%TYPE,   --Die ID des Vorganges
                            pPalFaktor      in ARTIKEL_EINHEIT.PAL_FAKTOR%type,
                            rPlanArt        in AUFTRAG_ART_PLANUNG%rowtype,
                            pOptResBes      in Boolean,                         --True wenn auch im Bestand reserviert werden soll
                            pPlanStep       in integer)                         --
                            return integer is
  aktlevel   PLS_INTEGER;

  v_komm_gw	 KOMM_POS.GEWICHT_SOLL%TYPE;
  v_gw       LAGER_BESTAND_SUMME.NETTO_GEWICHT%TYPE;
  v_bes_vpe  ARTIKEL_VPE.REF%TYPE;
  v_in_anz   ARTIKEL_EINHEIT.INHALT_ANZAHL%TYPE;

  v_ok       BOOLEAN;

  v_vorres   PLS_INTEGER;
  v_count    PLS_INTEGER;
  v_test     PLS_INTEGER;
  v_used     PLS_INTEGER;
  v_menge    PLS_INTEGER;
  v_add_res  PLS_INTEGER;
  v_max_used PLS_INTEGER;
  v_add_used PLS_INTEGER;

  res        INTEGER;
begin
  res := 0;

  aktlevel := BASE_TRACING.ENTERPROC ('BUCHE_RES_BESTAND');

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (2,'vr_auf_pos_ae.REF='||vr_auf_pos_ae.REF);
    BASE_TRACING.TraceOutput (2,'vr_ar_einheit.REF='||vr_ar_einheit.REF);
    BASE_TRACING.TraceOutput (2,'vr_bes_sum:   REF='||vr_bes_sum.REF||', REF_AR_EINHEIT='||vr_bes_sum.REF_AR_EINHEIT||', MENGE_FREI='||vr_bes_sum.MENGE_FREI);
    BASE_TRACING.TraceOutput (2,'pMinMHD          ='||to_char (pMinMHD, 'dd.mm.yyyy'));
    BASE_TRACING.TraceOutput (2,'pStockRequire    ='||pStockRequire);
    BASE_TRACING.TraceOutput (2,'v_benoetigt      ='||v_benoetigt);
    BASE_TRACING.TraceOutput (2,'v_vpe_benoetigt  ='||v_vpe_benoetigt);
    BASE_TRACING.TraceOutput (2,'pID              ='||pID);
    BASE_TRACING.TraceOutput (2,'pPalFaktor       ='||pPalFaktor);
    BASE_TRACING.TraceOutput (2,'pOptResBes       ='||BooleanToStr (pOptResBes));
    BASE_TRACING.TraceOutput (2,'pPlanStep        ='||pPlanStep);
  end if;

  select sum (MENGE_RES) into v_vorres from LAGER_VOR_RES_BESTAND where REF_AUF_POS=prAufPos.REF and REF_BES_SUM=vr_bes_sum.REF;
  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_vorres='||v_vorres);

  if (vr_bes_sum.REF is null) then
    v_menge := v_vpe_benoetigt;
  else
    if (v_vpe_benoetigt > (vr_bes_sum.MENGE_FREI - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - (nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_vorres, 0)) - nvl (pStockRequire, 0))) then
      v_menge := vr_bes_sum.MENGE_FREI - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - (nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_vorres, 0)) - nvl (pStockRequire, 0);
    else
      v_menge := v_vpe_benoetigt;
    end if;
  end if;

  --Gewicht bestimmen
  if (vr_bes_sum.REF is null) then
    if (vr_ar_einheit.REF is not null) then
      v_gw := vr_ar_einheit.NETTO_GEWICHT;
    else
      select NETTO_GEWICHT into v_gw from ARTIKEL_EINHEIT where REF=vr_auf_pos_ae.REF;
    end if;

    v_komm_gw := v_gw * v_menge;
  elsif (nvl (vr_bes_sum.NETTO_GEWICHT, 0) = 0) then
    if (vr_ar_einheit.REF = vr_bes_sum.REF_AR_EINHEIT) then
      v_gw := vr_ar_einheit.NETTO_GEWICHT;
    else
      select NETTO_GEWICHT into v_gw from ARTIKEL_EINHEIT where REF=vr_bes_sum.REF_AR_EINHEIT;
    end if;

    v_komm_gw := v_gw * v_menge;
  elsif (nvl (vr_bes_sum.MENGE_FREI, 0) > 0) then
    v_komm_gw := (vr_bes_sum.NETTO_GEWICHT / vr_bes_sum.MENGE_FREI) * v_menge;
  else
    if (vr_ar_einheit.REF = vr_bes_sum.REF_AR_EINHEIT) then
      v_gw := vr_ar_einheit.NETTO_GEWICHT;
    else
      select NETTO_GEWICHT into v_gw from ARTIKEL_EINHEIT where REF=vr_bes_sum.REF_AR_EINHEIT;
    end if;

    v_komm_gw := v_gw * v_menge;
  end if;

  --Wenn Res-Einheit = Used-Einheit
  if (vr_ar_einheit.REF = vr_auf_pos_ae.REF) Then
    v_used := v_menge; --Keine Stückbildung
  else
    if ((v_menge * nvl (vr_ar_einheit.INHALT_ANZAHL, 1)) > v_benoetigt) then
      v_used := v_benoetigt;
    else
      v_used := v_menge * nvl (vr_ar_einheit.INHALT_ANZAHL, 1);
    end if;
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_menge='||v_menge||', v_used='||v_used||', v_komm_gw='||v_komm_gw);
  end if;

  --Zu pickende Einheit bestimmen
  if (vr_bes_sum.REF is null) then
    v_bes_vpe := vr_ar_einheit.REF_EINHEIT;
  else
    if (vr_ar_einheit.REF = vr_bes_sum.REF_AR_EINHEIT) then
      v_bes_vpe := vr_ar_einheit.REF_EINHEIT;
    else
      select REF_EINHEIT into v_bes_vpe from ARTIKEL_EINHEIT where REF=vr_bes_sum.REF_AR_EINHEIT;
    end if;
  end if;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bes_sum.REF_AR='||vr_bes_sum.REF_AR||', v_bes_vpe='||v_bes_vpe||', vr_auf_pos_ae: REF_AR='||vr_auf_pos_ae.REF_AR||', REF_EINHEIT='||vr_auf_pos_ae.REF_EINHEIT);
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_auf_pos_ae.REF_AR='||vr_auf_pos_ae.REF_AR||', REF_EINHEIT='||vr_auf_pos_ae.REF_EINHEIT);
  end if;

  --Reservierung anlegen
  if (nvl (vr_ar_einheit.OPT_MULTI_COLLI, '0') = '1') then
    if (pID is null) then
      null;
    else
      declare
        v_buch_menge  PLS_INTEGER;
        v_menge_sku   PLS_INTEGER;
        v_set_menge   PLS_INTEGER;
        v_sum_res     PLS_INTEGER;
        v_sum_pick    PLS_INTEGER;
        v_set_res_id  LAGER_RES_BESTAND.RES_ID%TYPE;
        v_sammel_ref  LAGER_RES_BESTAND.REF%TYPE;
      begin
        v_set_menge  := v_menge;
        v_sammel_ref := null;

        loop
          v_ok := True;

          v_menge_sku := null;

          --Anzahl der benötigne, vollständigen SKUs ermitteln
          for cr_colli in (select * from ARTIKEL_EINHEIT_COLLI where STATUS='AKT' and REF_MASTER_AR_EINHEIT=vr_ar_einheit.REF) loop
            --Ermitteln, wie viel bereits reserviert wurde
            select sum (MENGE_RES) into v_sum_res from LAGER_RES_BESTAND where REF_AUF_POS=prAufPos.REF and USED_AR_EINHEIT=cr_colli.REF_SET_AR_EINHEIT;

            --Ermitteln, wie viel bereits gepickt wurde
            select sum (MENGE_PICK) into v_sum_pick from AUFTRAG_KOMM_POS where REF_AUF_POS=prAufPos.REF and REF_AR_EINHEIT_PICK=cr_colli.REF_SET_AR_EINHEIT;

            --Dies von der Gesamtmenge für die Auftragsposition abgezogen
            v_buch_menge := cr_colli.MENGE * prAufPos.MENGE_SOLL - nvl (v_sum_pick, 0) - nvl (v_sum_res, 0);

            --Die kleinste, noch benötige Menge ermitteln
            if (v_menge_sku is null or (v_buch_menge > v_menge_sku)) then
              v_menge_sku := v_buch_menge;
            end if;
          end loop;

          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_menge_sku='||v_menge_sku);
          end if;

          --Wenn mindestens eine ganze SKU benötigt wird
          if (nvl (v_menge_sku, 0) > 0) then
            --Suchen nach den LEs auf welchen ganze SKUs mit allen zugehörigen Collis liegen
            delete from TMP_KOMM_RES_PLAN;

            for cr_colli_bes in (select
                                     bes.REF_LE
                                   from
                                     LAGER_BESTAND bes
                                     --Muss auf einer LE liegen
                                     inner join LAGER_LE le on (le.REF=bes.REF_LE)
                                     --Die LE muss eingelagert sein
                                     inner join LAGER_LP lp on (lp.REF=le.REF_LP)
                                     --Der Bereich muss für die Komm freigegeben sein
                                     inner join TMP_KOMM_PLAN_LB plan on (plan.REF_LB=lp.REF_LB)
                                   where
                                     --Muss zum gewählten Summenbestand gehören
                                     bes.REF_BES_SUM=vr_bes_sum.REF and
                                     --Muss auf einer LE liegen
                                     bes.REF_LE is not null and
                                     --Bestand wurde bereits vereinnahmt
                                     bes.STATUS='AKT' and
                                     --Es ist noch nicht alles reserviert
                                     (nvl (bes.MENGE_FREI, 0) - nvl (bes.MENGE_RES, 0)) > 0 and
                                     --Alle passenden Colli-Bestände auswählen
                                     bes.REF_AR_EINHEIT in (select REF_SET_AR_EINHEIT from ARTIKEL_EINHEIT_COLLI where STATUS='AKT' and REF_MASTER_AR_EINHEIT=vr_ar_einheit.REF)) loop
              --Prüfen, ob die LE schon erfasst wurde
              select count (*) into v_count from TMP_KOMM_RES_PLAN where REF_LE=cr_colli_bes.REF_LE;

              if (v_count = 0) then
                declare
                  v_col_menge PLS_INTEGER;
                  v_min_menge PLS_INTEGER;
                  v_res_menge PLS_INTEGER;
                  v_ges_res   PLS_INTEGER;
                  v_ges_menge PLS_INTEGER;
                begin
                  select sum (MENGE_FREI), sum (MENGE_RES) into v_ges_menge, v_ges_res from LAGER_BESTAND where STATUS='AKT' and REF_LE=cr_colli_bes.REF_LE;

                  select sum (MENGE_RES) into v_res_menge from LAGER_BESTAND where STATUS='AKT' and REF_LE=cr_colli_bes.REF_LE and REF_AR=vr_ar_einheit.REF_AR;

                  v_min_menge := null;

                  --Für alles Collis die Mengen ermitteln
                  for cr_colli in (select * from ARTIKEL_EINHEIT_COLLI where STATUS='AKT' and REF_MASTER_AR_EINHEIT=vr_ar_einheit.REF) loop
                    --Summe aller verfügbaren Mengen ermitteln, reservierte und noch nicht vereinnahmte Mengen werden nicht berücksichtigt
                    select nvl (sum (nvl (MENGE_FREI, 0) - nvl (MENGE_RES, 0)), 0) into v_col_menge from LAGER_BESTAND where STATUS='AKT' and REF_LE=cr_colli_bes.REF_LE and REF_AR_EINHEIT=cr_colli.REF_SET_AR_EINHEIT;

                    if (lTraceLevel >= 5) then
                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_colli.REF_SET_AR_EINHEIT='||cr_colli.REF_SET_AR_EINHEIT||', v_col_menge='||v_col_menge);
                    end if;

                    --Die geringste, verfügbare Menge ermitteln
                    if (v_min_menge is null or (v_min_menge > v_col_menge)) then
                      v_min_menge := v_col_menge;
                    end if;
                  end loop;

                  --Ergebnis ablegen
                  insert into TMP_KOMM_RES_PLAN
                      (REF_LE, MIN_MENGE_FREI, MENGE_RES, MENGE_GESAMT, MENGE_GESAMT_RES)
                    values
                      (cr_colli_bes.REF_LE, v_min_menge, v_res_menge, v_ges_menge, v_ges_res);
                end;
              end if;
            end loop;

            if (lTraceLevel >= 5) then
              for cr_tmp in (select
                                 *
                               from
                                 TMP_KOMM_RES_PLAN
                               order by
                                 case
                                   when MIN_MENGE_FREI=1 then
                                     0
                                   when MIN_MENGE_FREI=0 then
                                     1
                                   when MIN_MENGE_FREI>1 then
                                     100 + MIN_MENGE_FREI
                                   else
                                     99999
                                 end asc,
                                 (nvl (MENGE_GESAMT, 0) - nvl (MENGE_GESAMT_RES, 0)) asc,
                                 MENGE_RES desc nulls last) loop
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_tmp : REF_LE='||cr_tmp.REF_LE||', LE_NR='||PA_LE.GetLENummer (cr_tmp.REF_LE)||', MIN_MENGE_FREI='||cr_tmp.MIN_MENGE_FREI||', MENGE_RES='||cr_tmp.MENGE_RES||', MENGE_GESAMT='||cr_tmp.MENGE_GESAMT||', MENGE_GESAMT_RES='||cr_tmp.MENGE_GESAMT_RES);
              end loop;
            end if;

            --Die SKU mit allen Collis von der LE reserviern, die min. Menge 1 enthält
            for cr_tmp in (select                             *
                             from
                               TMP_KOMM_RES_PLAN
                             order by
                               case
                                 when MIN_MENGE_FREI=1 then
                                   0
                                 when MIN_MENGE_FREI=0 then
                                   1
                                 when MIN_MENGE_FREI>1 then
                                   100 + MIN_MENGE_FREI
                                 else
                                   99999
                               end asc,
                               (nvl (MENGE_GESAMT, 0) - nvl (MENGE_GESAMT_RES, 0)) asc,
                               MENGE_RES desc nulls last) loop
              --Neue RES_ID erzeugen
              select SEQ_ID.NEXTVAL into v_set_res_id from dual;
              insert into TMP_PLAN_RES_ID (RES_ID) values (v_set_res_id);

              if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_res_id='||v_set_res_id); end if;

              --Für jedes Colli
              for cr_colli in (select * from ARTIKEL_EINHEIT_COLLI where STATUS='AKT' and REF_MASTER_AR_EINHEIT=vr_ar_einheit.REF) loop
                declare
                  v_res_menge   PLS_INTEGER;
                begin
                  if (v_sammel_ref is null) then
                    --Wenn ganze Colli-Sets reserviert wurden, wird dafür eine Summenreservierung angelegt
                    insert into LAGER_RES_BESTAND
                        (RES_ID, REF_BES_SUM, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, USED_AR_EINHEIT, CHARGE_SOLL)
                      VALUES
                        (pID, vr_bes_sum.REF, prAufPos.REF, 0, vr_bes_sum.REF_AR_EINHEIT, vr_bes_sum.REF_AR_EINHEIT, vr_bes_sum.CHARGE)
                      return REF into v_sammel_ref;
                  end if;

                  --Es wird immer nur die Menge des einzelnen Collis gebucht
                  v_res_menge := cr_colli.MENGE;

                  res := BucheColliMenge (vr_bes_sum.REF, cr_colli.REF_SET_AR_EINHEIT, prAufPos.REF, pMinMHD, v_set_res_id, cr_tmp.REF_LE, pPalFaktor, rPlanArt, v_res_menge);

                  exit when (res <> 0);

                  --Wenn nicht die vollständige Menge reserviert werden konnte, kann das hier beendet werden
                  if (cr_colli.MENGE <> v_res_menge) then
                    v_ok := False;
                    exit;
                  end if;
                end;
              end loop;

              --Die RES_ID der Fehlermeldungen muss umgesetzt werden
              update TMP_BES_RES_FEHLER set RES_ID=pID where RES_ID=v_set_res_id;

              if (v_ok) and (v_sammel_ref is not null) then
                --Menge in der Sammelreservierung um 1 erhöhen
                update LAGER_RES_BESTAND set MENGE_RES=MENGE_RES + 1 where REF=v_sammel_ref;

                --Alle Reservierungen müssen jetzt auf die RES_ID der Planung umgebucht werden
                for cr_colli_res in (select * from LAGER_RES_BESTAND where RES_ID=v_set_res_id) loop
                  declare
                    vr_res LAGER_RES_BESTAND%rowtype;
                  begin
                    begin
                      --Prüfen ob es schon einen passenden Eintrag in LAGER_RES_BESTAND gibt
                      select * into vr_res from LAGER_RES_BESTAND where RES_ID=pID and REF_SET_RES=v_sammel_ref and REF_BESTAND=cr_colli_res.REF_BESTAND and REF_AUF_POS=prAufPos.REF;

                      exception
                        when no_data_found then
                          vr_res := null;
                        when others then
                          raise;
                    end;

                    if (vr_res.REF is null) then
                      --und die Einzelreservierungen zeigen darauf
                      update LAGER_RES_BESTAND set RES_ID=pID, REF_SET_RES=v_sammel_ref where REF=cr_colli_res.REF;
                    else
                      --Reservierte Menge in die bereits angelegten LAGER_RES_BESTAND übernehmen
                      update LAGER_RES_BESTAND set MENGE_RES=nvl (MENGE_RES, 0)+cr_colli_res.MENGE_RES, MENGE_USED=nvl (MENGE_USED, 0)+cr_colli_res.MENGE_USED where REF=vr_res.REF;

                      --Mengen und REF_BESTAND zurücksetzen, sonst schlägt der Trigger zu
                      update LAGER_RES_BESTAND set REF_BESTAND=null, MENGE_RES=null, MENGE_USED=null where REF=cr_colli_res.REF;

                      delete from LAGER_RES_BESTAND where REF=cr_colli_res.REF;
                    end if;
                  end;
                end loop;

                --Gilt auch für die Temptabelle
                update TMP_KOMM_RES set RES_ID=pID where RES_ID=v_set_res_id;

                --Es wurde ein weiteres Colli-Set reserviert
                v_set_menge := v_set_menge - 1;

                if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_menge='||v_set_menge); end if;
              else
                --Hier dann noch die überzähligen Reservierungen wieder freigeben!
                res := RESET_RESERVIERUNG (v_set_res_id);
              end if;

              --Zur Zeit wird maximal eine vollständeige SKU gebucht
              exit;
            end loop;
          end if;

          exit when (res <> 0);

          --Wenn alle Colli-Set reserviert sind, ist hier Schluss
          exit when (v_set_menge <= 0);

          if (res = 0) then
            --Neue RES_ID erzeugen
            select SEQ_ID.NEXTVAL into v_set_res_id from dual;
            insert into TMP_PLAN_RES_ID (RES_ID) values (v_set_res_id);

            if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_res_id='||v_set_res_id); end if;

            --Reservierung der einzelnen Collis
            for cr_colli in (select * from ARTIKEL_EINHEIT_COLLI where STATUS='AKT' and REF_MASTER_AR_EINHEIT=vr_ar_einheit.REF) loop
              --Ermitteln, wie viel bereits reserviert wurde
              select sum (MENGE_RES) into v_sum_res from LAGER_RES_BESTAND where REF_AUF_POS=prAufPos.REF and USED_AR_EINHEIT=cr_colli.REF_SET_AR_EINHEIT;

              --Ermitteln, wie viel bereits gepickt wurde
              select sum (MENGE_PICK) into v_sum_pick from AUFTRAG_KOMM_POS where REF_AUF_POS=prAufPos.REF and REF_AR_EINHEIT_PICK=cr_colli.REF_SET_AR_EINHEIT;

              --Dies von der Gesamtmenge für die Auftragsposition abgezogen
              v_buch_menge := cr_colli.MENGE * prAufPos.MENGE_SOLL - nvl (v_sum_pick, 0) - nvl (v_sum_res, 0);

              if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_colli.REF_SET_AR_EINHEIT='||cr_colli.REF_SET_AR_EINHEIT||', aufmenge='||cr_colli.MENGE * prAufPos.MENGE_SOLL||', v_buch_menge='||v_buch_menge||', v_sum_pick='||v_sum_pick||', v_sum_res='||v_sum_res); end if;

              --Wenn überhaupt noch etwas benötigt wird
              if (v_buch_menge > 0) then
                declare
                  v_res_menge   PLS_INTEGER;
                begin
                  if (v_sammel_ref is null) then
                    --Wenn ganze Colli-Sets reserviert wurden, wird dafür eine Summenreservierung angelegt
                    insert into LAGER_RES_BESTAND
                        (RES_ID, REF_BES_SUM, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, USED_AR_EINHEIT, CHARGE_SOLL)
                      VALUES
                        (pID, vr_bes_sum.REF, prAufPos.REF, 0, vr_bes_sum.REF_AR_EINHEIT, vr_bes_sum.REF_AR_EINHEIT, vr_bes_sum.CHARGE)
                      return REF into v_sammel_ref;
                   end if;

                  --Es wird immer nur die Menge des einzelnen Collis gebucht
                  v_res_menge := cr_colli.MENGE;

                  res := BucheColliMenge (vr_bes_sum.REF, cr_colli.REF_SET_AR_EINHEIT, prAufPos.REF, pMinMHD, v_set_res_id, null, pPalFaktor, rPlanArt, v_res_menge);

                  exit when (res <> 0);

                  --Wenn nicht die vollständige Menge reserviert werden konnte, kann das hier beendet werden
                  if (cr_colli.MENGE <> v_res_menge) then
                    v_ok := False;
                    exit;
                  end if;
                end;
              end if;
            end loop;

            if (res = 0) then
              --Die RES_ID der Fehlermeldungen muss umgesetzt werden
              update TMP_BES_RES_FEHLER set RES_ID=pID where RES_ID=v_set_res_id;

              if (v_ok) and (v_sammel_ref is not null) then
                --Menge in der Sammelreservierung um 1 erhöhen
                update LAGER_RES_BESTAND set MENGE_RES=MENGE_RES + 1 where REF=v_sammel_ref;

                --Alle Reservierungen müssen jetzt auf die RES_ID der Planung umgebucht werden
                for cr_colli_res in (select * from LAGER_RES_BESTAND where RES_ID=v_set_res_id) loop
                  declare
                    vr_res LAGER_RES_BESTAND%rowtype;
                  begin
                    begin
                      --Prüfen ob es schon einen passenden Eintrag in LAGER_RES_BESTAND gibt
                      select * into vr_res from LAGER_RES_BESTAND where RES_ID=pID and REF_SET_RES=v_sammel_ref and REF_BESTAND=cr_colli_res.REF_BESTAND and REF_AUF_POS=prAufPos.REF;

                      exception
                        when no_data_found then
                          vr_res := null;
                        when others then
                          raise;
                    end;

                    if (vr_res.REF is null) then
                      --und die Einzelreservierungen zeigen darauf
                      update LAGER_RES_BESTAND set RES_ID=pID, REF_SET_RES=v_sammel_ref where REF=cr_colli_res.REF;
                    else
                      --Reservierte Menge in die bereits angelegten LAGER_RES_BESTAND übernehmen
                      update LAGER_RES_BESTAND set MENGE_RES=nvl (MENGE_RES, 0)+cr_colli_res.MENGE_RES, MENGE_USED=nvl (MENGE_USED, 0)+cr_colli_res.MENGE_USED where REF=vr_res.REF;

                      --Mengen und REF_BESTAND zurücksetzen, sonst schlägt der Trigger zu
                      update LAGER_RES_BESTAND set REF_BESTAND=null, MENGE_RES=null, MENGE_USED=null where REF=cr_colli_res.REF;

                      delete from LAGER_RES_BESTAND where REF=cr_colli_res.REF;
                    end if;
                  end;
                end loop;


                --Gilt auch für die Temptabelle
                update TMP_KOMM_RES set RES_ID=pID where RES_ID=v_set_res_id;

                --Es wurde ein weiteres Colli-Set reserviert
                v_set_menge := v_set_menge - 1;

                if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_menge='||v_set_menge); end if;

                --Wenn alle Colli-Set reserviert sind, ist hier Schluss
                exit when (v_set_menge <= 0);
              else
                --Hier dann noch die überzähligen Reservierungen wieder freigeben!
                res := RESET_RESERVIERUNG (v_set_res_id);

                exit;
              end if;
            end if;
          end if;
        end loop;

        if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_menge='||v_menge||', v_set_menge='||v_set_menge); end if;

        if (res = 0) then
          if (v_set_menge < v_menge) then
            --Reservierte Menge im Summenbestand vermerken
            update LAGER_BESTAND_SUMME_RES
              set
                MENGE_RES=NVL (MENGE_RES,0) + v_menge - v_set_menge
              where
                REF_BES_SUM=vr_bes_sum.REF
              returning MENGE_RES into v_test;

            if (v_test<0) then
              raise_application_error (-20203, 'RES-Fehler MENGE_RES='||v_test);
            end if;
          else
            --Die unbenutzte Sammelreservierung wieder löschen
            delete from LAGER_RES_BESTAND where REF=v_sammel_ref;
          end if;

          v_benoetigt := v_benoetigt - (v_menge - v_set_menge);
          v_vpe_benoetigt := v_vpe_benoetigt - (v_menge - v_set_menge);
        end if;
      end;
    end if;
  elsif (vr_bes_sum.REF is not null) and ((vr_bes_sum.REF_AR <> vr_auf_pos_ae.REF_AR) or (v_bes_vpe <> vr_auf_pos_ae.REF_EINHEIT)) then
    --Hier wird aus einer Umverpackung teile des Inhaltes reserviert
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Sammelres');
    end if;

    select INHALT_ANZAHL into v_in_anz from ARTIKEL_EINHEIT where REF=(select REF_AR_EINHEIT from LAGER_BESTAND_SUMME where REF=vr_bes_sum.REF);

    if (pID is null) then
      null;
    else
      for cr_sum_res in (select * from LAGER_RES_BESTAND where RES_ID is null and REF_BESTAND is not null and REF_BES_SUM is not null and REF_BES_SUM=vr_bes_sum.REF) loop
        v_max_used := cr_sum_res.MENGE_RES * v_in_anz;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': update cr_sum_res: REF='||cr_sum_res.REF||', MENGE_RES='||cr_sum_res.MENGE_RES||', v_in_anz='||v_in_anz||', v_max_used='||v_max_used);
        end if;

        if (v_max_used >= (cr_sum_res.MENGE_USED + v_used)) then
          v_add_used := v_used;
        elsif (cr_sum_res.MENGE_USED < v_max_used) then
          v_add_used := v_max_used - cr_sum_res.MENGE_USED;
        else
          v_add_used := 0;
        end if;

        if (v_add_used > 0) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_add_used='||v_add_used);
          end if;

          update LAGER_RES_BESTAND set MENGE_USED = cr_sum_res.MENGE_USED + v_add_used where REF=cr_sum_res.REF;

          insert into LAGER_RES_BESTAND
              (REF_SAMMEL_RES, RES_ID, REF_BES_SUM, REF_BESTAND, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, FLAG_VOLLPAL)
            VALUES
              (cr_sum_res.REF, pID, cr_sum_res.REF_BES_SUM, cr_sum_res.REF_BESTAND, prAufPos.REF, null, cr_sum_res.RES_AR_EINHEIT, v_add_used, cr_sum_res.USED_AR_EINHEIT, v_komm_gw, pMinMHD, '0');

          v_used := v_used - v_add_used;
        end if;

        exit when v_used <= 0;
      end loop;
    end if;

    if (v_used > 0) then
      v_add_res := (Ceil (v_used / v_in_anz));
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_add_res='||v_add_res);
      end if;

      res := BucheInhaltMenge (vr_bes_sum.REF, prAufPos, pMinMHD, pID, vr_auf_pos_ae.REF, v_in_anz, v_add_res, v_used);

      if (res = 0) and (v_used > 0) then
        v_benoetigt := v_benoetigt - v_used;
        v_vpe_benoetigt := v_vpe_benoetigt - ceil (v_used / v_in_anz);
      end if;
    else
      v_benoetigt := 0;
      v_vpe_benoetigt := 0;
    end if;
  elsif (vr_bes_sum.REF is not null) then
    --Hier gehts weiter, wenn nicht Stückartikel kommissioniert werden sollen

    v_count := 0;

    if pOptResBes then
      --Prüfen, ob es für den Artikel einen Kommissionierplatz gibt
      --select count (*) into v_count from KOMM_REL_AR_LP where REF_PLANUNG is null and REF_LAGER=vr_bes_sum.REF_LAGER and REF_AR=vr_bes_sum.REF_AR;
      null;
    end if;

    if not (pOptResBes) or (v_count > 0) then
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Komm LPs='||v_count);
      end if;

      if (pID is null) then
        null;
      else
        --Wenn ein Kommplatz definiert ist, reicht die Reservierung auf Bestandssummen-Ebene
        insert into LAGER_RES_BESTAND
            (RES_ID, REF_BES_SUM, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN)
          VALUES
            (pID, vr_bes_sum.REF, prAufPos.REF, v_menge, vr_ar_einheit.REF, v_used, vr_auf_pos_ae.REF, v_komm_gw, pMinMHD);

        if (vr_bes_sum.REF is not null) then
          --Reservierte Menge im Summenbestand vermerken
          update LAGER_BESTAND_SUMME_RES
            set
              MENGE_RES=NVL (MENGE_RES,0) + v_menge
            where
              REF_BES_SUM=vr_bes_sum.REF
            returning MENGE_RES into v_test;

            if (v_test<0) then
              raise_application_error (-20202, 'RES-Fehler MENGE_RES='||v_test);
            end if;
        end if;
      end if;

      v_benoetigt := v_benoetigt - v_menge;
      v_vpe_benoetigt := v_vpe_benoetigt - v_menge;
    else
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Keine Komm LPs');

      res := BucheMenge (vr_bes_sum.REF, prAufPos, pMinMHD, pID, pPalFaktor, rPlanArt, pPlanStep, v_menge);

      if (res = 0) and (v_menge > 0) then
        v_benoetigt := v_benoetigt - v_menge;
        v_vpe_benoetigt := v_vpe_benoetigt - v_menge;
      end if;
    end if;
  else
    if (pID is null) then
      null;
    else
      insert into LAGER_RES_BESTAND
          (RES_ID, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN)
        VALUES
          (pID, prAufPos.REF, v_menge, vr_ar_einheit.REF, v_used, vr_auf_pos_ae.REF, v_komm_gw, pMinMHD);
    end if;

    v_benoetigt := v_benoetigt - v_used;
    v_vpe_benoetigt := v_vpe_benoetigt - v_used;
  end if;

  BASE_TRACING.TraceOutput (2,'v_benoetigt    ='||v_benoetigt);
  BASE_TRACING.TraceOutput (2,'v_vpe_benoetigt='||v_vpe_benoetigt);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;


--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function FIND_AUFTRAG_POS_BESTAND  (vr_auf        in  AUFTRAG%ROWTYPE,
                                    vr_auf_pos    in  AUFTRAG_POS%ROWTYPE,
                                    prArEinheit   in  ARTIKEL_EINHEIT%ROWTYPE,
                                    pID           in  LAGER_RES_BESTAND.RES_ID%TYPE,
                                    pMHDToleranz  in  INTEGER,
                                    pKommOpt      in  varchar2,
                                    rPlanArt      in  AUFTRAG_ART_PLANUNG%rowtype,
                                    pMengeUse     in  PLS_INTEGER,
                                    oMengeRest    out PLS_INTEGER,
                                    oBesErrText   out nocopy varchar2,
                                    oErrorCode    OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN integer is
  aktlevel   PLS_INTEGER;

  vr_bes_sum     VQ_LAGER_BESTAND_SUMME_PLAN%ROWTYPE;
  vr_last_bes    VQ_LAGER_BESTAND_SUMME_PLAN%ROWTYPE;
  vr_artikel     ARTIKEL%ROWTYPE;

  v_auf_mhd      AUFTRAG_POS.MHD_MIN%TYPE;

  v_mhd          LAGER_BESTAND_SUMME.MHD%TYPE;
  v_pal_faktor   ARTIKEL_EINHEIT.PAL_FAKTOR%TYPE;
  v_ref_cat      LAGER_BESTAND_CATEGORY.REF%type;

  v_stock_req    INTEGER;
  v_ok           BOOLEAN;
  v_mhd_rein     BOOLEAN;

  res	           NUMBER;

  --*****************************************************************************
  --*  Function Name     : FIND_BESTAND
  --*  Author            : Stefan Graf
  --*****************************************************************************
  --*  Description       :
  --*---------------------------------------------------------------------------
  --*  Return Value      :
  --*****************************************************************************
  function FIND_BESTAND (vr_ar_einheit      in ARTIKEL_EINHEIT%ROWTYPE, --Diese Einheit wird gesuchen
                         vr_ar_einheit_used in ARTIKEL_EINHEIT%ROWTYPE, --Diese Einheit wird benötigt
                         pOptResBes         in Boolean,                 --True wenn auch im Bestand reserviert werden soll
                         rPlanArt           in AUFTRAG_ART_PLANUNG%rowtype,
                         v_benoetigt        in out INTEGER,             --Die Menge wird benötigt (vr_ar_einheit_used)
                         v_vpe_benoetigt    in out INTEGER,             --Diese Menge soll serviert werden (vr_ar_einheit)
                         oBesErrText        out nocopy varchar2)
                         return integer is

    aktlevel   PLS_INTEGER;

    vr_ar_rel_empf ARTIKEL_REL_WARENEMPF%ROWTYPE;

    vr_bes_sum    c_bestand_frei%ROWTYPE;
    v_pal_faktor  ARTIKEL_EINHEIT.PAL_FAKTOR%TYPE;
    v_ok          BOOLEAN;
    v_mhd_rein    BOOLEAN;
    v_plan_step   integer;

    v_count       PLS_INTEGER;
    v_count_bes   PLS_INTEGER;

    res           INTEGER;
   begin
    res := 0;

    aktlevel := BASE_TRACING.ENTERPROC ('FIND_BESTAND');

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (2,'vr_ar_einheit.REF      ='||vr_ar_einheit.REF);
      BASE_TRACING.TraceOutput (2,'vr_ar_einheit_used.REF ='||vr_ar_einheit_used.REF);
      BASE_TRACING.TraceOutput (2,'pOptResBes             ='||BooleanToStr (pOptResBes));
      BASE_TRACING.TraceOutput (2,'v_benoetigt            ='||v_benoetigt);
      BASE_TRACING.TraceOutput (2,'v_vpe_benoetigt        ='||v_vpe_benoetigt);
    end if;

    v_count_bes := 0;

    select * into vr_artikel from ARTIKEL where REF=vr_ar_einheit.REF_AR;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_artikel: REF='||vr_artikel.REF||', ARTIKEL_NR='||vr_artikel.ARTIKEL_NR||', OPT_BESTAND='||vr_artikel.OPT_BESTAND||', OPT_AUTO_WA_BUCHUNG='||vr_artikel.OPT_AUTO_WA_BUCHUNG||', vr_ar_einheit.OPT_MULTI_COLLI='||vr_ar_einheit.OPT_MULTI_COLLI);
    end if;

    if (nvl (vr_artikel.OPT_MHD_PFLICHT, 'O') = 'O') then
      v_auf_mhd := null;
    elsif (vr_auf_pos.MHD_MIN is not null) then
      --Wenn in der Auftragspos vorgegeben ist
      v_auf_mhd := vr_auf_pos.MHD_MIN;
    else
      begin
        select * into vr_ar_rel_empf from (select * from ARTIKEL_REL_WARENEMPF where REF_WARENEMPF=vr_auf.REF_WARENEMPF and (REF_AR is null or REF_AR=vr_ar_einheit.REF_AR) order by REF_AR nulls last) where ROWNUM=1;

        exception
          when no_data_found then
            vr_ar_rel_empf := null;
          when others then
            raise;
      end;

      --Wenn für den Warenempfänger selber nichts angegeben ist
      if (vr_ar_rel_empf.REF is null) then
        declare
          v_kd_nr AUFTRAG_ADR.NUMMER%type;
        begin
          --Dann noch die Kundennummer der Liefranschrift prüfen
          select NUMMER into v_kd_nr from AUFTRAG_ADR where REF=vr_auf.REF_LIEFER_ADR;

          if (v_kd_nr is not null) then
            begin
              select * into vr_ar_rel_empf from (select * from ARTIKEL_REL_WARENEMPF where REF_WARENEMPF is null and ANLIEF_KUNDEN_NUMMER=v_kd_nr and (REF_AR is null or REF_AR=vr_ar_einheit.REF_AR) order by REF_AR nulls last) where ROWNUM=1;

              exception
                when no_data_found then
                  vr_ar_rel_empf := null;
                when others then
                  raise;
            end;
          end if;
        end;
      end if;

      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_ar_rel_empf: REF='||vr_ar_rel_empf.REF||' RLZ_WA='||vr_ar_rel_empf.RLZ_WA);
      end if;

      if (vr_ar_rel_empf.RLZ_WA is not null) then
        --Wenn für den Warenempfänger eine Restlaufzeit vorgegeben ist
        v_auf_mhd := vr_auf.LIEFER_DATUM + vr_ar_rel_empf.RLZ_WA;
      elsif (vr_artikel.RLZ_KOMM is not null) then
        --Wenn für den Artikel eine Kommissionier Restlaufzeit vorgegeben ist
        v_auf_mhd := vr_auf.LIEFER_DATUM + vr_artikel.RLZ_KOMM;
      elsif (vr_artikel.RLZ_WA is not null) then
        --Wenn für den Artikel eine Anliefer Restlaufzeit vorgegeben ist
        v_auf_mhd := vr_auf.LIEFER_DATUM + vr_artikel.RLZ_WA;
      else
        v_auf_mhd := vr_auf.LIEFER_DATUM + 1;
      end if;
    end if;

    if (lTraceLevel >= 3) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': fix MHD='||TO_CHAR (vr_auf_pos.MHD_FIX, 'DD.MM.YYYY')||', min MHD='||TO_CHAR (v_auf_mhd, 'DD.MM.YYYY')||', AR_VARIANTE='||vr_auf_pos.AR_VARIANTE||', CHARGE='||vr_auf_pos.CHARGE);
    end if;

    if (nvl (vr_artikel.OPT_BESTAND, '1') = '0') then
      res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, vr_ar_einheit, null, null, null, v_benoetigt, v_vpe_benoetigt, pID, null, rPlanArt, pOptResBes, 2);
    elsif (vr_auf_pos.CROSSDOCK_BESTELL_NR is not null) then
      res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, null, null, null, null, v_benoetigt, v_vpe_benoetigt, pID, null, rPlanArt, pOptResBes, 2);
    elsif (nvl (vr_ar_einheit.OPT_MULTI_COLLI, '0') = '1') then
      --Nur wenn die geforderte Menge grösser als der Palettenfaktor ist, wird nach vollen Paletten gesucht
      if (vr_ar_einheit.PAL_FAKTOR is null) or (v_vpe_benoetigt < (vr_ar_einheit.PAL_FAKTOR * 80) / 100) then
        v_pal_faktor := null;
      else
        v_pal_faktor := vr_ar_einheit.PAL_FAKTOR;
      end if;

      --Wenn keine reine Vollplattenplanung möglich ist, wird direkt mit Step 2 begonnen
      if (nvl (rPlanArt.OPT_VOLL_PAL_PLAN, '0') = '0') or (v_pal_faktor is null) then
        v_plan_step := 2;
      else
        v_plan_step := 1;
      end if;

      loop
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': OPT_MULTI_COLLI=1: v_pal_faktor='||v_pal_faktor);
        end if;

        open c_colli_bestand_frei (vr_auf.REF_LAGER, vr_artikel.REF, vr_ar_einheit.REF_EINHEIT, vr_auf_pos.AR_VARIANTE);
        loop
          begin
            if (lTraceLevel >= 5) then
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt);
            end if;

            fetch c_colli_bestand_frei into vr_bes_sum;
            if (c_colli_bestand_frei%NOTFOUND) Then
              exit;
            else
              v_count_bes := v_count_bes + 1;

              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bes_sum: REF='||vr_bes_sum.REF||', MENGE_FREI='||vr_bes_sum.MENGE_FREI||' ,MENGE_WE='||vr_bes_sum.MENGE_WE||', MENGE_RES='||vr_bes_sum.MENGE_RES||', MENGE_VOR_RES='||vr_bes_sum.MENGE_VOR_RES);
              end if;

              v_ok := true;
              if (vr_auf_pos.CHARGE is not null and nvl (vr_artikel.OPT_CHARGE_SUMME, '0')='1' and (vr_bes_sum.CHARGE <> vr_auf_pos.CHARGE)) then
                v_ok := False;
              elsif (v_pal_faktor is not null) and (v_vpe_benoetigt >= v_pal_faktor) and ((vr_bes_sum.MENGE_FREI  - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) < v_pal_faktor) then
                v_ok := False;
              elsif (nvl (vr_bes_sum.MENGE_VOR_RES, 0) > 0) and ((vr_bes_sum.MENGE_FREI  - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) < 0) then
                v_ok := False;
              elsif ((vr_bes_sum.MENGE_FREI  - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) <= 0) then
                v_ok := False;
              end if;

              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ok='||BooleanToStr (v_ok));
              end if;

              if (v_ok) then
                select * into vr_bes_sum from VQ_LAGER_BESTAND_SUMME_PLAN where REF=vr_bes_sum.REF;

                res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, vr_ar_einheit, vr_bes_sum, v_auf_mhd, v_stock_req, v_benoetigt, v_vpe_benoetigt, pID, null, rPlanArt, pOptResBes, 2);

                if (v_vpe_benoetigt = 0) then
                  exit;
                end if;
              end if;
            end if;

            EXCEPTION WHEN OTHERS THEN
              close c_colli_bestand_frei;
              raise;
          end;

          exit when (res <> 0) or (oErrorCode <> 0);
        end loop;

        close c_colli_bestand_frei;

        exit when (res <> 0) or (oErrorCode <> 0);

        exit when (v_vpe_benoetigt <= 0);

        exit when not (v_mhd_rein) and v_pal_faktor is null;

        if not (v_mhd_rein) then
          v_pal_faktor := null;
        else
          v_mhd_rein   := FALSE;
          v_pal_faktor := null;
        end if;
      end loop;
    else
      if (nvl (vr_artikel.OPT_MHD_PFLICHT, 'O') = 'O') then
        --Bei Artikeln ohne MHD muss da gar nicht darauf geachtet werden
        v_mhd_rein := False;
      elsif (vr_auf_pos.MHD_FIX is not null) then
        --Bei fest vorgegebenem MHD muss nicht nach weiteren MHDs gesucht werden
        v_mhd_rein := False;
      elsif (true=false) then
        --Nur wenn mehr als ein Stück gesucht wird, wird überhaupt eine MHD-Mischung in Betracht gezogen
        v_mhd_rein := (v_vpe_benoetigt > 1);
      else
        v_mhd_rein := False;
      end if;

      --Nur wenn die geforderte Menge grösser als der Palettenfaktor ist, wird nach vollen Paletten gesucht
      if (vr_ar_einheit.PAL_FAKTOR is null) or (v_vpe_benoetigt < (vr_ar_einheit.PAL_FAKTOR * 80) / 100) then
        v_pal_faktor := null;
      else
        v_pal_faktor := vr_ar_einheit.PAL_FAKTOR;
      end if;

      vr_last_bes := null;
      v_mhd       := null;

      loop
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': OPT_MULTI_COLLI=0: v_mhd_rein='||BooleanToStr (v_mhd_rein)||', v_pal_faktor='||v_pal_faktor);
        end if;

        --Wenn keine reine Vollplattenplanung möglich ist, wird direkt mit Step 2 begonnen
        if (nvl (rPlanArt.OPT_VOLL_PAL_PLAN, '0') = '0') or (v_pal_faktor is null) then
          v_plan_step := 2;
        else
          v_plan_step := 1;
        end if;

        loop
          open c_bestand_frei (vr_auf.REF_LAGER, vr_artikel.REF, vr_ar_einheit.REF_EINHEIT, vr_auf_pos.AR_VARIANTE, v_benoetigt, '0');
          loop
            begin
              if (lTraceLevel >= 3) then
                BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt);
              end if;

              fetch c_bestand_frei into vr_bes_sum;
              if (c_bestand_frei%NOTFOUND) Then
                exit;
              else
                v_count_bes := v_count_bes + 1;

                if (lTraceLevel >= 3) then
                  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bes_sum: REF='||vr_bes_sum.REF||'; REF_CATEGORY='||vr_bes_sum.REF_CATEGORY||', MENGE_FREI='||vr_bes_sum.MENGE_FREI||', MENGE_WE='||vr_bes_sum.MENGE_WE||', MENGE_RES='||vr_bes_sum.MENGE_RES||', MENGE_VOR_RES='||vr_bes_sum.MENGE_VOR_RES);

                  if (vr_bes_sum.MHD is null) then
                    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bes_sum: MHD='||to_char (null)||', CHARGE'||vr_bes_sum.CHARGE);
                  else
                    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bes_sum: MHD='||TO_CHAR (vr_bes_sum.MHD, 'DD.MM.YYYY')||' ('||TO_CHAR (vr_bes_sum.MHD + pMHDToleranz, 'DD.MM.YYYY')||')'||', CHARGE='||vr_bes_sum.CHARGE);
                  end if;
                end if;

                v_ok := true;
                if (vr_auf_pos.MHD_FIX is not null) and (vr_bes_sum.MHD <> vr_auf_pos.MHD_FIX) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : MHD_FIX');
                  end if;
                elsif (vr_auf_pos.CHARGE is not null and nvl (vr_artikel.OPT_CHARGE_SUMME, '0')='1' and (vr_bes_sum.CHARGE <> vr_auf_pos.CHARGE)) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : CHARGE');
                  end if;
                elsif (v_mhd_rein) and ((vr_bes_sum.MENGE_FREI - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) < v_vpe_benoetigt) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : MENGE_FREI');
                  end if;
                elsif (v_pal_faktor is not null and nvl (rPlanArt.OPT_ANBRUCH_VOLL_PAL, '0') = '0') and (v_vpe_benoetigt >= v_pal_faktor) and ((vr_bes_sum.MENGE_FREI  - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) < v_pal_faktor) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : PAL_FAKTOR');
                  end if;
                elsif (nvl (substr (vr_auf_pos.OPTIONS, 7, 1), '0') = '1') and (v_vpe_benoetigt >= nvl (vr_ar_einheit.PAL_FAKTOR, 0)) and (nvl (vr_bes_sum.MENGE_FREI, 0) < nvl (vr_ar_einheit.PAL_FAKTOR, 0)) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : PAL_FAKTOR');
                  end if;
                elsif ((vr_bes_sum.MENGE_FREI  - nvl (vr_bes_sum.MENGE_RES, 0) - nvl (vr_bes_sum.MENGE_WE, 0) - nvl (vr_bes_sum.MENGE_VOR_RES, 0) - nvl (v_stock_req, 0)) < 1) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : MENGE_FREI');
                  end if;
                elsif (v_ref_cat is null and vr_bes_sum.REF_CATEGORY is not null) or (v_ref_cat is not null and v_ref_cat<>nvl (vr_bes_sum.REF_CATEGORY, -1)) then
                  v_ok := False;
                  if (lTraceLevel >= 3) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Fail : CATEGORY');
                  end if;
                end if;

                if (lTraceLevel >= 3) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ok='||BooleanToStr (v_ok));
                end if;

                if (v_ok) then
                  if (vr_auf_pos.MHD_FIX is not null) then
                    --select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=vr_bes_sum.REF;

                    res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, vr_ar_einheit, vr_bes_sum, vr_auf_pos.MHD_FIX, v_stock_req, v_benoetigt, v_vpe_benoetigt, pID, v_pal_faktor, rPlanArt, pOptResBes, v_plan_step);
                  elsif (v_auf_mhd is null) or (vr_bes_sum.MHD is null) or (vr_bes_sum.MHD >= v_auf_mhd) then
                    --select * into vr_bes_sum from LAGER_BESTAND_SUMME where REF=vr_bes_sum.REF;

                    res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, vr_ar_einheit, vr_bes_sum, v_auf_mhd, v_stock_req, v_benoetigt, v_vpe_benoetigt, pID, v_pal_faktor, rPlanArt, pOptResBes, v_plan_step);
                  else
                    if (lTraceLevel >= 3) then
                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ok='||BooleanToStr (v_ok));
                    end if;

                    if (v_mhd is null) or (v_mhd < vr_bes_sum.MHD) then
                      vr_last_bes := vr_bes_sum;
                      v_mhd       := vr_bes_sum.MHD;
                    end if;
                  end if;

                  if (v_vpe_benoetigt = 0) then
                    exit;
                  end if;
                end if;
              end if;

              EXCEPTION WHEN OTHERS THEN
                close c_bestand_frei;
                raise;
            end;

            exit when (res <> 0) or (oErrorCode <> 0);
          end loop;

          close c_bestand_frei;

          exit when (res <> 0) or (oErrorCode <> 0);

          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_vpe_benoetigt='||v_vpe_benoetigt||', v_plan_step='||v_plan_step);
          end if;

          exit when (v_vpe_benoetigt <= 0);

          if (v_plan_step = 1) then
            v_plan_step := 2;
          else
            exit;
          end if;
        end loop;

        exit when (res <> 0) or (oErrorCode <> 0);

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_vpe_benoetigt='||v_vpe_benoetigt||', v_plan_step='||v_plan_step||', v_mhd_rein='||case when v_mhd_rein then 'true' else 'false' end||', v_pal_faktor='||v_pal_faktor);
        end if;

        exit when (v_vpe_benoetigt <= 0);
        exit when not (v_mhd_rein) and v_pal_faktor is null;

        if not (v_mhd_rein) then
          v_pal_faktor := null;
        else
          v_mhd_rein   := FALSE;
          v_pal_faktor := null;
        end if;
      end loop;

      --Wenn gar kein Bestand gefunden wurde
      if (v_count_bes = 0) then
        --Prüfen ob es noch Bestand mit Status='ANG' gibt
        select count (*) into v_count from LAGER_BESTAND where STATUS='ANG' and REF_BES_SUM is null and REF_LAGER=vr_auf.REF_LAGER and REF_AR=vr_artikel.REF;

        if (v_count > 0) then
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, vr_auf_pos.REF, vr_artikel.REF, vr_auf_pos.REF_AR_EINHEIT, 32, 'offene WEs vorgehalten');
        end if;
      elsif (v_vpe_benoetigt > 0) then
        --Prüfen ob es für den Artikel noch offene WEs gibt
        select count (*) into v_count from WARENEINGANG
          where
            STATUS='ANG' and
            REF_MAND=vr_auf.REF_MAND and
            REF_LAGER=vr_auf.REF_LAGER and
            REF in (select REF_WE from WE_POS
                      where
                        REF_AR=vr_auf_pos.REF_AR and ((AR_VARIANTE is null and vr_auf_pos.AR_VARIANTE is null) or (AR_VARIANTE=vr_auf_pos.AR_VARIANTE)));

        if (v_count > 0) then
          insert into TMP_BES_RES_FEHLER
              (RES_ID, REF_AUF_POS, REF_AR, REF_AR_EINHEIT, FEHLER_CODE, FEHLER_INFO)
            values
              (pID, vr_auf_pos.REF, vr_artikel.REF, vr_auf_pos.REF_AR_EINHEIT, 33, 'offene WEs vorhanden');
        end if;
      else
        --Wenn ausreichen Bestanmd reserviert wurde, werden alle Fehlermeldungen zu der Auftragspos. gelöscht, damit die nicht falsch interpretiert werden
        delete from TMP_BES_RES_FEHLER where REF_AUF_POS=vr_auf_pos.REF;
      end if;

      if (v_vpe_benoetigt > 0) and (pMHDToleranz is not null) and (pMHDToleranz > 0) Then
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Last');
          BASE_TRACING.TraceOutput (5,'        v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt||', vr_last_bes.REF='||vr_last_bes.REF);
        end if;

        if (vr_last_bes.REF is not null) then
          select * into vr_bes_sum from VQ_LAGER_BESTAND_SUMME_PLAN where REF= vr_last_bes.REF;

          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5,'      vr_last_bes: MENGE_FREI='||(nvl(vr_last_bes.MENGE_FREI,0) - nvl (vr_last_bes.MENGE_RES, 0) - nvl (vr_last_bes.MENGE_WE, 0))||', MHD='||TO_CHAR (vr_last_bes.MHD, 'DD.MM.YYYY')||' ('||TO_CHAR (vr_last_bes.MHD + pMHDToleranz, 'DD.MM.YYYY')||')'||', CHARGE'||vr_last_bes.CHARGE);
          end if;

          if (v_auf_mhd is null) or ((vr_last_bes.MHD + pMHDToleranz) >= v_auf_mhd) then
            res := BUCHE_RES_BESTAND (vr_auf_pos, vr_ar_einheit_used, vr_ar_einheit, vr_last_bes, v_auf_mhd, v_stock_req, v_benoetigt, v_vpe_benoetigt, pID, null, rPlanArt, pOptResBes, 2);
          end if;
        end if;
      end if;
    end if;

    if (nvl (v_stock_req, 0) > 0) and (nvl (v_vpe_benoetigt, 0) > 0) then
      if (Length (oBesErrText) > 0) then oBesErrText := oBesErrText ||', '; end if;
      oBesErrText := oBesErrText||v_stock_req||' Einheiten vorgehalten';
    end if;

    if (v_vpe_benoetigt > 0) then
      if (vr_auf_pos.MHD_FIX is not null) then
        if (Length (oBesErrText) > 0) then oBesErrText := oBesErrText ||', '; end if;
        oBesErrText := oBesErrText||' Fix-MHD ('||to_char (vr_auf_pos.MHD_FIX,'DD.MM.YYYY');
      elsif (v_mhd is not null and nvl (vr_artikel.OPT_MHD_PFLICHT, 'O') <> 'N') then
        if (Length (oBesErrText) > 0) then oBesErrText := oBesErrText ||', '; end if;
        oBesErrText := oBesErrText||'(´MHD ('||to_char (v_mhd,'DD.MM.YYYY')||') < '||to_char (v_auf_mhd,'DD.MM.YYYY')||')';
      end if;
    end if;

    BASE_TRACING.TraceOutput (2,'v_benoetigt    ='||v_benoetigt);
    BASE_TRACING.TraceOutput (2,'v_vpe_benoetigt='||v_vpe_benoetigt);
    BASE_TRACING.TraceOutput (2,'oBesErrText    ='||oBesErrText);

    BASE_TRACING.LEAVEPROC (res);

    return res;
  end;

  --*****************************************************************************
  --*  Function Name     : FIND_REST_BESTAND
  --*  Author            : Stefan Graf
  --*****************************************************************************
  --*  Description       :
  --*---------------------------------------------------------------------------
  --*  Return Value      :
  --*****************************************************************************
  function FIND_REST_BESTAND (vr_ar_einheit      in ARTIKEL_EINHEIT%ROWTYPE, --Diese Einheit wird gesuchen
                              vr_ar_einheit_used in ARTIKEL_EINHEIT%ROWTYPE, --Diese Einheit wird benötigt
                              v_benoetigt        in out INTEGER,             --Die Menge wird benötigt (vr_ar_einheit_used)
                              oBesErrText        out nocopy varchar2)
                              return integer is

	  aktlevel   PLS_INTEGER;

    v_ok          BOOLEAN;

    res           INTEGER;
   begin
    res := 0;

    aktlevel := BASE_TRACING.ENTERPROC ('FIND_REST_BESTAND');

    if (lTraceLevel >= 2) then
      BASE_TRACING.TraceOutput (2,'vr_ar_einheit.REF      ='||vr_ar_einheit.REF);
      BASE_TRACING.TraceOutput (2,'vr_ar_einheit_used.REF ='||vr_ar_einheit_used.REF);
      BASE_TRACING.TraceOutput (2,'v_benoetigt            ='||v_benoetigt);
    end if;

    select * into vr_artikel from ARTIKEL where REF=vr_ar_einheit.REF_AR;
    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5,'#'||$$plsql_line||': ARTIKEL.REF='||vr_artikel.REF||' / ARTIKEL.ARTIKEL_NR='||vr_artikel.ARTIKEL_NR);
    end if;

    if (nvl (vr_artikel.OPT_MHD_PFLICHT, 'O') = 'O') then
      v_auf_mhd := null;
    elsif (vr_auf_pos.MHD_MIN is null) then
      if (vr_artikel.RLZ_KOMM is not null) then
        v_auf_mhd := vr_auf.LIEFER_DATUM + vr_artikel.RLZ_KOMM;
      elsif (vr_artikel.RLZ_WA is not null) then
        v_auf_mhd := vr_auf.LIEFER_DATUM + vr_artikel.RLZ_WA;
      else
        v_auf_mhd := null;
      end if;
    else
      v_auf_mhd := vr_auf_pos.MHD_MIN;
    end if;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5,'#'||$$plsql_line||': fix MHD='||TO_CHAR (vr_auf_pos.MHD_FIX, 'DD.MM.YYYY')||', minMHD='||TO_CHAR (v_auf_mhd, 'DD.MM.YYYY')||', AR_VARIANTE='||vr_auf_pos.AR_VARIANTE);
    end if;

    if (pID is null) then
      null;
    else
      --Suchen nach einer Sammelreservierung für den Umkarton im Kommissionierlager
      for cr_res in (select * from LAGER_RES_BESTAND where RES_ID is null and RES_AR_EINHEIT=vr_ar_einheit.REF and REF_BES_SUM in (select REF from LAGER_BESTAND_SUMME where REF_AR=vr_artikel.REF and REF_LAGER=vr_auf.REF_LAGER)) loop
        declare
          v_ok      BOOLEAN;
          v_used    integer;
          v_komm_gw integer;
          v_ref     LAGER_RES_BESTAND.REF%TYPE;
          vr_ae     ARTIKEL_EINHEIT%ROWTYPE;
        begin
          select * into vr_ae from ARTIKEL_EINHEIT where REF=cr_res.RES_AR_EINHEIT;

          if (lTraceLevel >= 5) then
            BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': cr_res.REF='||cr_res.REF||', cr_res.MENGE_RES='||cr_res.MENGE_RES||', cr_res.MENGE_USED='||cr_res.MENGE_USED||', max='||(cr_res.MENGE_RES * vr_ae.INHALT_ANZAHL));
          end if;

          --Prüfen, ob noch Stückmengen frei sind
          if (cr_res.MENGE_USED < (cr_res.MENGE_RES * vr_ae.INHALT_ANZAHL)) then
            declare
              v_mhd LAGER_BESTAND_SUMME.MHD%TYPE;
              v_var LAGER_BESTAND_SUMME.AR_VARIANTE%TYPE;
            begin
              select MHD, AR_VARIANTE into v_mhd, v_var from LAGER_BESTAND_SUMME where REF=cr_res.REF_BES_SUM;
              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (5,'#'||$$plsql_line||': Bes MHD='||TO_CHAR (v_mhd, 'DD.MM.YYYY')||', v_var='||v_var);
              end if;

              v_ok := False;

              if (vr_auf_pos.MHD_FIX is null) or (vr_bes_sum.MHD = vr_auf_pos.MHD_FIX) then
                --Die Variante prüfen
                if (vr_auf_pos.AR_VARIANTE is null and v_var is null) or (vr_auf_pos.AR_VARIANTE = v_var) then
                  --Wenn MHD nicht geprüft werden muss
                  if (v_auf_mhd is null) then
                    v_ok := True;
                  else
                    --MHD des Bestandes auslesen

                    if (v_mhd >= v_auf_mhd) then
                      v_ok := True;
                    end if;
                  end if;
                end if;
              end if;
            end;

            if (lTraceLevel >= 5) then
              if (v_ok) then BASE_TRACING.tracetimestamp (5,'#'||$$plsql_line||': v_ok=true');else BASE_TRACING.tracetimestamp (5,'#'||$$plsql_line||': v_ok=false');end if;
            end if;

            if (v_ok) then
              --Zusätzlich zu reservierende Menge bestimmen
              if (v_benoetigt > ((cr_res.MENGE_RES * vr_ae.INHALT_ANZAHL) - cr_res.MENGE_USED)) then
                v_used := (cr_res.MENGE_RES * vr_ae.INHALT_ANZAHL) - cr_res.MENGE_USED;
              else
                v_used := v_benoetigt;
              end if;

              --Nettogewicht ausrechenen
              select NETTO_GEWICHT * v_used into v_komm_gw from ARTIKEL_EINHEIT where REF=vr_ae.REF_INHALT;

              --In der Sammelreservierung die zusätzliche Menge eintragen
              update LAGER_RES_BESTAND set MENGE_USED=MENGE_USED+v_used where REF=cr_res.REF;

              if (lTraceLevel >= 5) then
                BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': update LAGER_RES_BESTAND');
              end if;

              --Eine neue Reservierungsposition anlegen
              insert into LAGER_RES_BESTAND
                  (RES_ID, REF_SAMMEL_RES, REF_BES_SUM, REF_BESTAND, REF_AUF_POS, MENGE_RES, RES_AR_EINHEIT, MENGE_USED, USED_AR_EINHEIT, NETTO_GEWICHT, MHD_MIN, REF_LB, REF_LE, REF_LP)
                VALUES
                  (pID, cr_res.REF, cr_res.REF_BES_SUM, cr_res.REF_BESTAND, vr_auf_pos.REF, null, cr_res.RES_AR_EINHEIT, v_used, cr_res.USED_AR_EINHEIT, v_komm_gw, v_auf_mhd, cr_res.REF_LB, cr_res.REF_LE, cr_res.REF_LP)
                returning REF into v_ref;

              if (lTraceLevel >= 5) then
                BASE_TRACING.tracetimestamp (5,'#'||$$plsql_line||': v_ref='||v_ref);
              end if;

              --Es wird maximal eine VPE zusätzlich reserviert
              v_benoetigt     := v_benoetigt - v_used;
            end if;
          end if;
        end;

        exit when (v_benoetigt <= 0);
      end loop;
    end if;

    BASE_TRACING.TraceOutput (2,'v_benoetigt='||v_benoetigt);
    BASE_TRACING.TraceOutput (2,'oBesErrText='||oBesErrText);

    BASE_TRACING.LEAVEPROC (res);

    return res;
  end;

begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  oBesErrText := '';

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.FIND_AUFTRAG_POS_BESTAND');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'prArEinheit.REF ='||prArEinheit.REF);
    BASE_TRACING.TraceOutput (2,'pMengeUse       ='||pMengeUse);
    BASE_TRACING.TraceOutput (2,'pKommOpt        ='||pKommOpt);
  end if;

  v_ref_cat := null;

  if (lTraceLevel >= 5) then
    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_auf_pos : REF='||vr_auf_pos.REF||', POS_NR='||vr_auf_pos.POS_NR||', CATEGORY_DEFINITION='||vr_auf_pos.CATEGORY_DEFINITION);
  end if;

  if (vr_auf_pos.CATEGORY_DEFINITION is not null) then
    begin
      select REF into v_ref_cat from LAGER_BESTAND_CATEGORY where REF_MAND=vr_auf.REF_MAND and DEFINITION=vr_auf_pos.CATEGORY_DEFINITION;

      exception
        when no_data_found then
          v_ref_cat := null;
        when others then
          raise;
    end;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_ref_cat='||v_ref_cat);
    end if;
  end if;


  declare
    vr_ar           ARTIKEL%ROWTYPE;
    vr_ar_einheit   ARTIKEL_EINHEIT%ROWTYPE;

    v_res_bes       boolean;
    v_benoetigt     PLS_INTEGER;
    v_vpe_benoetigt PLS_INTEGER;
    v_bes_text      varchar2 (1024);
  begin
    vr_ar_einheit := prArEinheit;
    select * into vr_ar from ARTIKEL where REF=prArEinheit.REF_AR;

    v_benoetigt     := pMengeUse;
    v_vpe_benoetigt := pMengeUse;

    v_mhd := NULL;
    vr_last_bes := null;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_ar.OPT_AUTO_WA_BUCHUNG='||vr_ar.OPT_AUTO_WA_BUCHUNG||', PA_LAGER.KOMM_OPT_RES_BESTAND='||substr (pKommOpt, PA_LAGER.KOMM_OPT_RES_BESTAND, 1));
    end if;

    if (vr_ar.OPT_AUTO_WA_BUCHUNG = '1') then
      v_res_bes := false;
    elsif (nvl (substr (pKommOpt, PA_LAGER.KOMM_OPT_RES_BESTAND, 1), '0') = '1') then
      --Wenn nur in der Summe reserviert werden soll
      v_res_bes := false;
    else
      v_res_bes := true;
    end if;

    begin
      select
          MIN_VPE into v_stock_req
        from
          (select
               rel.MIN_VPE
             from
               AUFTRAG_ART_REL_ARTIKEL rel inner join ARTIKEL a on (a.REF=vr_auf_pos.REF_AR) left outer join ARTIKEL_REL_GRUPPE arg on (REF_PARENT=rel.REF_AR_GRUPPE)
             where
               rel.STATUS='AKT' and
               rel.REF_MAND=vr_auf.REF_MAND and
               rel.REF_LAGER=vr_auf.REF_LAGER and
               rel.AUFTRAG_ART=vr_auf.Auftragsart and
               (
                (rel.REF_AR is null and rel.REF_AR_GRUPPE is null) or
                (rel.REF_AR is not null and rel.REF_AR=a.REF) or
                (rel.REF_AR_GRUPPE is not null and (a.REF_GRUPPE=rel.REF_AR_GRUPPE or arg.REF_GRUPPE=a.REF_GRUPPE))
               )
             order by
               rel.REF_AR nulls last, case when (a.REF_GRUPPE=rel.REF_AR_GRUPPE) then 1 else 0 end desc, arg.REF_GRUPPE nulls last
          );

      exception
        when no_data_found then
          v_stock_req := null;
        when others then
          raise;
    end;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_res_bes='||case when v_res_bes then 'true' else 'false' end ||', v_stock_req='||v_stock_req||', prArEinheit.STUECK_KENNZEICHEN='||prArEinheit.STUECK_KENNZEICHEN);
    end if;

    --Als erstes versuchen die Stückmengen auf Kartons zu verdichten
    if (prArEinheit.STUECK_KENNZEICHEN = '1') then
      declare
        v_ka_soll      integer;
        v_ka_benoetigt integer;
        v_komm_opt     LAGER_LB.KOMM_OPT%TYPE;
      begin
        if (pKommOpt is null) or (substr (pKommOpt, PA_LAGER.KOMM_OPT_ITEM_COMPACTED, 1) = '1') then
          begin
            SELECT
                LB_KOMM_OPT into v_komm_opt
              FROM
              (
                select
                  lb.KOMM_OPT as LB_KOMM_OPT
                from
                  KOMM_REL_AR_LP rlp, KOMM_LP klp, LAGER_LP lp, LAGER_LB lb
                WHERE
                  lp.REF=klp.REF_LP and
                  lb.REF=lp.REF_LB and
                  klp.REF=rlp.REF_KOMM_LP and
                  rlp.REF_PLANUNG is null and
                  rlp.REF_LAGER=vr_auf.REF_LAGER and
                  rlp.REF_AR=prArEinheit.REF_AR and
                  rlp.REF_EINHEIT=prArEinheit.REF_EINHEIT
                group by
                  lb.KOMM_OPT
               ) where
                  ROWNUM=1;
            exception
              when no_data_found then
                v_komm_opt := null;
              when others then
                raise;
          end;

          if (lTraceLevel >= 5) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_komm_opt ='||v_komm_opt);
          end if;

          --if ((v_komm_opt is not null) and (substr (v_komm_opt, 5, 1) = '1')) then
            for vr_ka in (select * from ARTIKEL_EINHEIT where STATUS='AKT' and (nvl (INHALT_ANZAHL, 0) > 0) and REF_INHALT=prArEinheit.REF and REF_AR in (select REF_AR from ARTIKEL_REL_LAGER where STATUS in ('AKT','MAN') and REF_LAGER=vr_auf.REF_LAGER)) loop
              if (v_vpe_benoetigt >= vr_ka.INHALT_ANZAHL) then
                v_ka_benoetigt := floor (v_vpe_benoetigt / vr_ka.INHALT_ANZAHL);

                if (lTraceLevel >= 5) then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_ka : REF='||vr_ka.REF||', inhalt_anzahl='||vr_ka.inhalt_anzahl||', v_ka_benoetigt='||v_ka_benoetigt);
                end if;

                v_ka_soll := v_ka_benoetigt;

                res := FIND_BESTAND (vr_ka, vr_ka, v_res_bes, rPlanArt, v_ka_benoetigt, v_ka_benoetigt, v_bes_text);

                if (v_bes_text is not null) then
                  if (oBesErrText is null) then
                    oBesErrText := v_bes_text;
                  elsif (instr (oBesErrText, v_bes_text) = 0) then
                    oBesErrText := oBesErrText ||','||v_bes_text;
                  end if;
                end if;

                if (lTraceLevel >= 5) then
                  BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': oBesErrText='||oBesErrText);
                end if;

                if (res = 0) then
                  if (lTraceLevel >= 5) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_ka_benoetigt='||v_ka_benoetigt);
                  end if;

                  v_benoetigt     := v_benoetigt     - ((v_ka_soll - v_ka_benoetigt) * vr_ka.INHALT_ANZAHL);
                  v_vpe_benoetigt := v_vpe_benoetigt - ((v_ka_soll - v_ka_benoetigt) * vr_ka.INHALT_ANZAHL);

                  if (lTraceLevel >= 5) then
                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt);
                  end if;
                end if;
              end if;

              exit when (res <> 0) or (oErrorCode <> 0);
              exit when (v_vpe_benoetigt <= 0);
            end loop;
          end if;
        --end if;
      end;
    end if;

    if (lTraceLevel >= 5) then
      BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': KA : v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt);
    end if;

    if (v_vpe_benoetigt > 0) then
      res := FIND_BESTAND (vr_ar_einheit, prArEinheit, v_res_bes, rPlanArt, v_benoetigt, v_vpe_benoetigt, v_bes_text);

      if (res = 0) then
        if (v_bes_text is not null) then
          if (oBesErrText is null) then
            oBesErrText := v_bes_text;
          elsif (instr (oBesErrText, v_bes_text) = 0) then
            oBesErrText := oBesErrText ||','||v_bes_text;
          end if;
        end if;
        if (lTraceLevel >= 5) then
          BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||':oBesErrText='||oBesErrText);
        end if;

        if (v_vpe_benoetigt > 0) then
          if (lTraceLevel >= 5) then
            BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': Umverpackung suchen');
          end if;

          if (vr_ar_einheit.REF_AR = vr_auf_pos.REF_AR) then
            if (lTraceLevel >= 3) then
              BASE_TRACING.tracetimestamp (3, '#'||$$plsql_line||': Selber Artikel, andere Einheit');
            end if;

            for vr_inhalt in (select * from ARTIKEL_EINHEIT where STATUS='AKT' and REF_INHALT=vr_ar_einheit.REF and nvl (INHALT_ANZAHL,0) > 0  and REF_AR in (select REF_AR from ARTIKEL_REL_LAGER where STATUS in ('AKT','MAN') and REF_LAGER=vr_auf.REF_LAGER)) loop
              res := FIND_REST_BESTAND (vr_inhalt, prArEinheit, v_benoetigt, v_bes_text);
              exit when (res <> 0) or (oErrorCode <> 0);

              if (v_bes_text is not null) then
                if (oBesErrText is null) then
                  oBesErrText := v_bes_text;
                elsif (instr (oBesErrText, v_bes_text) = 0) then
                  oBesErrText := oBesErrText ||','||v_bes_text;
                end if;
              end if;

              if (lTraceLevel >= 5) then
                BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': oBesErrText='||oBesErrText);
              end if;

              if (v_benoetigt <= 0) then
                v_vpe_benoetigt := 0;
              else
                v_vpe_benoetigt := ceil (v_benoetigt / vr_inhalt.INHALT_ANZAHL);

                res := FIND_BESTAND (vr_inhalt, prArEinheit, v_res_bes, rPlanArt, v_benoetigt, v_vpe_benoetigt, v_bes_text);
                exit when (res <> 0) or (oErrorCode <> 0);

                if (v_bes_text is not null) then
                  if (oBesErrText is null) then
                    oBesErrText := v_bes_text;
                  elsif (instr (oBesErrText, v_bes_text) = 0) then
                    oBesErrText := oBesErrText ||','||v_bes_text;
                  end if;
                end if;

                if (lTraceLevel >= 5) then
                  BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': oBesErrText='||oBesErrText);
                end if;
              end if;

              exit when (v_vpe_benoetigt <= 0);
            end loop;

            v_vpe_benoetigt := v_benoetigt;
          else
            if (lTraceLevel >= 5) then
              BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': Anderer Artikel');
            end if;

            for vr_inhalt in (select * from ARTIKEL_EINHEIT where STATUS='AKT' and REF_INHALT=vr_ar_einheit.REF and nvl (INHALT_ANZAHL,0) > 0 and REF_AR=vr_auf_pos.REF_AR and REF_AR in (select REF_AR from ARTIKEL_REL_LAGER where STATUS in ('AKT','MAN') and REF_LAGER=vr_auf.REF_LAGER)) loop
              res := FIND_REST_BESTAND (vr_inhalt, prArEinheit, v_benoetigt, v_bes_text);
              exit when (res <> 0) or (oErrorCode <> 0);

              if (v_bes_text is not null) then
                if (oBesErrText is null) then
                  oBesErrText := v_bes_text;
                elsif (instr (oBesErrText, v_bes_text) = 0) then
                  oBesErrText := oBesErrText ||','||v_bes_text;
                end if;
              end if;
              if (lTraceLevel >= 5) then
                BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': oBesErrText='||oBesErrText);
              end if;

              if (v_benoetigt <= 0) then
                v_vpe_benoetigt := 0;
              else
                v_vpe_benoetigt := ceil (v_benoetigt / vr_inhalt.INHALT_ANZAHL);

                res := FIND_BESTAND (vr_inhalt, prArEinheit, v_res_bes, rPlanArt, v_benoetigt, v_vpe_benoetigt, v_bes_text);
                exit when (res <> 0) or (oErrorCode <> 0);

                if (v_bes_text is not null) then
                  if (oBesErrText is null) then
                    oBesErrText := v_bes_text;
                  elsif (instr (oBesErrText, v_bes_text) = 0) then
                    oBesErrText := oBesErrText ||','||v_bes_text;
                  end if;
                end if;
                if (lTraceLevel >= 5) then
                  BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': oBesErrText='||oBesErrText);
                end if;
              end if;

              exit when (v_vpe_benoetigt <= 0);
            end loop;

            v_vpe_benoetigt := v_benoetigt;
          end if;
        end if;
      end if;

      if (lTraceLevel >= 5) then
        BASE_TRACING.tracetimestamp (5, '#'||$$plsql_line||': v_benoetigt='||v_benoetigt||', v_vpe_benoetigt='||v_vpe_benoetigt);
      end if;
    end if;

    oMengeRest := v_vpe_benoetigt;
  end;

  BASE_TRACING.TraceOutput (2, 'oMengeRest='||oMengeRest);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_AUFTRAG_POS_BESTAND (vr_auf        in  AUFTRAG%ROWTYPE,
                                    vr_auf_cfg    in  AUFTRAG_ART_CONFIG%ROWTYPE,
                                    vr_auf_pos    in  AUFTRAG_POS%ROWTYPE,
                                    pID           in  LAGER_RES_BESTAND.RES_ID%TYPE,
                                    pMHDToleranz  in  INTEGER,
                                    pKommOpt      in  varchar2,
                                    rPlanArt      in  AUFTRAG_ART_PLANUNG%rowtype,
                                    oFehlerStatus in  out nocopy FEHLER_LISTE.FEHLER_STATUS%TYPE,
                                    oErrorCode    OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN integer is
  aktlevel   PLS_INTEGER;

  vr_artikel     ARTIKEL%ROWTYPE;
  vr_auf_pos_ae  ARTIKEL_EINHEIT%ROWTYPE;

  v_auf_mhd      AUFTRAG_POS.MHD_MIN%TYPE;

  v_tab          PLS_INTEGER;
	v_count				 PLS_INTEGER;
  v_benoetigt    PLS_INTEGER;
  v_rest         PLS_INTEGER;

  v_besstext     varchar2 (1024);

  errtxt		     VARCHAR2 (256);

  res	           NUMBER;

  --***************************************************************************
  --*  Function Name     :
  --*  Author            : Stefan Graf
  --***************************************************************************
  --*  Description       :
  --*--------------------------------------------------------------------------
  --*  Return Value      :
  --***************************************************************************
  procedure InsertBestandsfehler (pID in LAGER_RES_BESTAND.RES_ID%TYPE, prArtikel in ARTIKEL%ROWTYPE, prArEinheit in ARTIKEL_EINHEIT%ROWTYPE, pSollMenge in PLS_INTEGER, pFehlMenge PLS_INTEGER, pErrText in varchar2) is
    aktlevel PLS_INTEGER;

    v_stat   FEHLER_POS.FEHLER_STATUS%TYPE;

    v_tab       PLS_INTEGER;
    v_tab_count PLS_INTEGER;

    errtxt	 VARCHAR2 (1024);
  begin
    aktlevel := BASE_TRACING.ENTERPROC ('InsertBestandsfehler');
    if (lTraceLevel >= 2) then
      BASE_TRACING.TraceOutput (2,'pID            ='||pID);
      BASE_TRACING.TraceOutput (2,'prArtikel.REF  ='||prArtikel.REF);
      BASE_TRACING.TraceOutput (2,'prArEinheit.REF='||prArEinheit.REF);
      BASE_TRACING.TraceOutput (2,'pFehlMenge     ='||pFehlMenge);
      BASE_TRACING.TraceOutput (2,'pSollMenge     ='||pSollMenge);
      BASE_TRACING.TraceOutput (2,'pErrText       ='||pErrText);
    end if;

    if (prArEinheit.REF_AR <> vr_auf_pos.REF_AR) then
      errtxt := 'Fehlender Set-Bestand';
    elsif (nvl (prArEinheit.OPT_MULTI_COLLI, '0') = '1') then
      errtxt := 'Fehlender Colli-Bestand';
    else
      errtxt := 'Fehlender Bestand';
    end if;

    if (pErrText is not null) then
      errtxt := errtxt||', '||pErrText;
    end if;

    v_tab_count := 0;

    v_tab := FehlerTab.last;
    if (v_tab is NULL) Then
      v_tab := 1;
    else
      v_tab := v_tab + 1;
    end if;

    v_stat := null;

    if (lTraceLevel >= 5) then
      BASE_TRACING.TraceOutput (3,'pID='||pID||', vr_auf_pos.REF='||vr_auf_pos.REF);
    end if;

    for cr_ae in (select REF_AR_EINHEIT from TMP_BES_RES_FEHLER where RES_ID=pID and REF_AUF_POS=vr_auf_pos.REF group by REF_AR_EINHEIT) loop
      if (lTraceLevel >= 5) then
        BASE_TRACING.TraceOutput (3,'cr_ae.REF_AR_EINHEIT='||cr_ae.REF_AR_EINHEIT);
      end if;

      for cr_fehler in (select distinct FEHLER_CODE, FEHLER_INFO from TMP_BES_RES_FEHLER where RES_ID=pID and REF_AUF_POS=vr_auf_pos.REF and REF_AR_EINHEIT=cr_ae.REF_AR_EINHEIT) loop
        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (3,'cr_fehler.FEHLER_CODE='||cr_fehler.FEHLER_CODE);
        end if;

        if (cr_fehler.FEHLER_CODE = 1) then
          v_stat := 'WE_EIN';
        elsif (cr_fehler.FEHLER_CODE = 2) then
          v_stat := 'EINLAGER';
        elsif (cr_fehler.FEHLER_CODE = 3) then
          v_stat := 'RET_EIN';
        elsif (cr_fehler.FEHLER_CODE = 8) then
          v_stat := 'INVENTUR';
        elsif (cr_fehler.FEHLER_CODE = 6) then
          v_stat := 'BEREICH';
        elsif (cr_fehler.FEHLER_CODE in (32, 33)) then
          v_stat := 'WE-OFFEN';
        elsif (cr_fehler.FEHLER_CODE in (15,16)) then
          v_stat := 'UMLAGERN';
        elsif (cr_fehler.FEHLER_CODE in (14,17,18)) then
          v_stat := 'NACHSCHUB';
        end if;

        if (instr (errtxt, cr_fehler.FEHLER_INFO) = 0) then
          errtxt := substr (errtxt||', '||cr_fehler.FEHLER_INFO, 1, 1024);
        end if;
      end loop;

      --Prüfen ob es für den Artikel noch Vorreservierungen
      declare
        v_vor_res PLS_INTEGER;
      begin
        select count (*), sum (MENGE_VOR_RES) into v_count, v_vor_res from VQ_LAGER_BESTAND_SUMME_PLAN
          where
            REF_MAND=vr_auf.REF_MAND and
            REF_LAGER=vr_auf.REF_LAGER and
            REF_AR=vr_auf_pos.REF_AR and
            nvl (MENGE_VOR_RES, 0) > 0 and
            ((nvl (prArtikel.OPT_MHD_PFLICHT, 'O')) = 'O' or (MHD >= v_auf_mhd));

        if (v_count > 0) then
          errtxt := substr (errtxt||', '||v_count||' Vorreservierungen ('||v_vor_res||' VPE)', 1, 1024);
        end if;
      end;

      --Prüfen ob es für den Artikel noch offene WEs gibt
      select count (*) into v_count from WARENEINGANG
        where
          STATUS='ANG' and
          REF_MAND=vr_auf.REF_MAND and
          REF_LAGER=vr_auf.REF_LAGER and
          REF in (select REF_WE from WE_POS
                    where
                      REF_AR=vr_auf_pos.REF_AR and ((AR_VARIANTE is null and vr_auf_pos.AR_VARIANTE is null) or (AR_VARIANTE=vr_auf_pos.AR_VARIANTE)));

      if (v_count > 0) then
        errtxt := substr (errtxt||', es gibt aber noch offene WEs für den Artikel', 1, 256);
      else
        select count (*) into v_count from BESTELLUNG
          where
            STATUS='ANG' and
            Trunc (LIEFER_DATUM)=Trunc (sysdate) and
            REF_MAND=vr_auf.REF_MAND and
            REF_LAGER=vr_auf.REF_LAGER and
            REF in (select REF_BEST_KOPF from BESTELL_POS
                      where
                        REF_AR=vr_auf_pos.REF_AR and ((AR_VARIANTE is null and vr_auf_pos.AR_VARIANTE is null) or (AR_VARIANTE=vr_auf_pos.AR_VARIANTE)));

        if (v_count > 0) then
          errtxt := substr (errtxt||', WE steht noch aus', 1, 1024);
        end if;
      end if;

      --BASE_TRACING.TraceOutput (0, errtxt);

      FehlerTab (v_tab).RefPos       := vr_auf_pos.REF;
      FehlerTab (v_tab).RefKopf      := vr_auf_pos.REF_AUF_KOPF;
      FehlerTab (v_tab).RefArEinheit := prArEinheit.REF;
      FehlerTab (v_tab).PosNr        := vr_auf_pos.POS_NR;
      FehlerTab (v_tab).Status       := nvl (v_stat, 'BESTAND');
      FehlerTab (v_tab).Text         := errtxt;
      FehlerTab (v_tab).SollMenge    := pSollMenge;
      FehlerTab (v_tab).FehlMenge    := pFehlMenge;
      FehlerTab (v_tab).Gewicht      := vr_auf_pos.GEWICHT_SOLL;

      v_tab := v_tab + 1;
      v_tab_count := v_tab_count + 1;
    end loop;

    if (v_tab_count = 0) then
      oFehlerStatus := 'BESTAND';

      FehlerTab (v_tab).RefPos       := vr_auf_pos.REF;
      FehlerTab (v_tab).RefKopf      := vr_auf_pos.REF_AUF_KOPF;
      FehlerTab (v_tab).RefArEinheit := prArEinheit.REF;
      FehlerTab (v_tab).PosNr        := vr_auf_pos.POS_NR;
      FehlerTab (v_tab).Status       := nvl (v_stat, 'BESTAND');
      FehlerTab (v_tab).Text         := errtxt;
      FehlerTab (v_tab).SollMenge    := pSollMenge;
      FehlerTab (v_tab).FehlMenge    := pFehlMenge;
      FehlerTab (v_tab).Gewicht      := vr_auf_pos.GEWICHT_SOLL;
    end if;

    BASE_TRACING.LeaveProcProc;
  end;

begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_AUFTRAG_POS_BESTAND');

  if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2, 'vr_auf.REF    ='||vr_auf.REF);
    BASE_TRACING.TraceOutput (2, 'vr_auf_pos.REF='||vr_auf_pos.REF);
    BASE_TRACING.TraceOutput (2, 'pID           ='||pID);
    BASE_TRACING.TraceOutput (2, 'pMHDToleranz  ='||pMHDToleranz);
    BASE_TRACING.TraceOutput (2, 'pKommOpt      ='||pKommOpt);
  end if;

  begin
    if (vr_auf_pos.REF_AR_EINHEIT is null) Then
      if (lTraceLevel >= 3) then
        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_pos.REF_AR='||vr_auf_pos.REF_AR||', vr_auf_pos.REF_EINHEIT='||vr_auf_pos.REF_EINHEIT);
      end if;

      select * into vr_auf_pos_ae from ARTIKEL_EINHEIT where STATUS='AKT' and nvl (OPT_MASTER,'0')='1' and REF_AR=vr_auf_pos.REF_AR and REF_EINHEIT=vr_auf_pos.REF_EINHEIT;
    else
      select * into vr_auf_pos_ae from ARTIKEL_EINHEIT where REF=vr_auf_pos.REF_AR_EINHEIT;

      if (vr_auf_pos_ae.REF_AR <> vr_auf_pos.REF_AR) then
        oErrorCode := 4;
        oErrorText := 'Der zugeordnete Artikel in Position '||vr_auf_pos.POS_NR||' ist nicht eindeutig';
      elsif (vr_auf_pos_ae.REF_EINHEIT <> vr_auf_pos.REF_EINHEIT) then
        oErrorCode := 4;
        oErrorText := 'Die zugeordnete Artikeleinheit bei Artikel '||PA_ARTIKEL.GETARTIKELNR (vr_auf_pos_ae.REF_AR)||' in Position '||vr_auf_pos.POS_NR||' ist nicht eindeutig';
      end if;
    end if;

    exception
      when no_data_found then
        vr_auf_pos_ae := null;

      when others then
        raise;
  end;

  if (oErrorCode = 0) then
    if (lTraceLevel >= 3) then
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> vr_auf_pos : MENGE_BESTELLT='||vr_auf_pos.MENGE_BESTELLT||', MENGE_SOLL='||vr_auf_pos.MENGE_SOLL||', MENGE_GEPLANT='||vr_auf_pos.MENGE_GEPLANT||', GEWICHT_SOLL='||vr_auf_pos.GEWICHT_SOLL||', MENGE_GEPLANT='||vr_auf_pos.MENGE_GEPLANT);
    end if;

    if (vr_auf_pos.MENGE_BESTELLT is not null) then
      v_benoetigt     := NVL (vr_auf_pos.MENGE_SOLL, 0) - NVL (vr_auf_pos.MENGE_GEPLANT, 0);
    elsif (vr_auf_pos.GEWICHT_SOLL is not null) then
      if (nvl (vr_auf_pos.GEWICHT_GEPLANT, 0) < vr_auf_pos.GEWICHT_SOLL) then
        if (nvl (vr_auf_pos_ae.OPT_GEWICHT, '0') = '0') then
          if (vr_auf_pos_ae.NETTO_GEWICHT is null) then
            v_benoetigt := 1;
          else
            v_benoetigt := ceil ((vr_auf_pos.GEWICHT_SOLL - nvl (vr_auf_pos.GEWICHT_GEPLANT, 0)) / vr_auf_pos_ae.NETTO_GEWICHT);
          end if;
        else
          if (vr_auf_pos_ae.NETTO_GEWICHT is null) then
            v_benoetigt := 1;
          else
            v_benoetigt := ceil ((vr_auf_pos.GEWICHT_SOLL - nvl (vr_auf_pos.GEWICHT_GEPLANT, 0)) / vr_auf_pos_ae.NETTO_GEWICHT);
          end if;
        end if;
      else
        v_benoetigt := 0;
      end if;
    else
      v_benoetigt := 0;
    end if;

    if (v_benoetigt > 0) Then
      if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5,'#'||$$plsql_line||': POS_NR='||vr_auf_pos.POS_NR);
    end if;

      if (vr_auf_pos_ae.REF is null) Then
        oFehlerStatus := 'ARTIKEL';

        v_tab := FehlerTab.last;
        if (v_tab is NULL) Then
          v_tab := 1;
        else
          v_tab := v_tab + 1;
        end if;

        FehlerTab (v_tab).RefPos       := vr_auf_pos.REF;
        FehlerTab (v_tab).RefKopf      := vr_auf_pos.REF_AUF_KOPF;
        FehlerTab (v_tab).PosNr        := vr_auf_pos.POS_NR;
        FehlerTab (v_tab).RefArEinheit := vr_auf_pos.REF_AR_EINHEIT;
        FehlerTab (v_tab).Status       := 'ARTIKEL';
        FehlerTab (v_tab).Text         := 'Artikel unbekannt';
        FehlerTab (v_tab).SollMenge    := v_benoetigt;
        FehlerTab (v_tab).FehlMenge    := v_benoetigt;
        FehlerTab (v_tab).Gewicht      := vr_auf_pos.GEWICHT_SOLL;
      elsif (vr_auf_pos_ae.STATUS <> 'AKT') Then
        oFehlerStatus := 'ARTIKEL';

        v_tab := FehlerTab.last;
        if (v_tab is NULL) Then
          v_tab := 1;
        else
          v_tab := v_tab + 1;
        end if;

        FehlerTab (v_tab).RefPos       := vr_auf_pos.REF;
        FehlerTab (v_tab).RefKopf      := vr_auf_pos.REF_AUF_KOPF;
        FehlerTab (v_tab).PosNr        := vr_auf_pos.POS_NR;
        FehlerTab (v_tab).RefArEinheit := vr_auf_pos.REF_AR_EINHEIT;
        FehlerTab (v_tab).Status       := 'ARTIKEL';
        FehlerTab (v_tab).Text         := 'Artikel ist nicht mehr aktive';
        FehlerTab (v_tab).SollMenge    := v_benoetigt;
        FehlerTab (v_tab).FehlMenge    := v_benoetigt;
        FehlerTab (v_tab).Gewicht      := vr_auf_pos.GEWICHT_SOLL;
      else
        select * into vr_artikel from ARTIKEL where REF=vr_auf_pos_ae.REF_AR;

        if (lTraceLevel >= 5) then
          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_artikel.REF_ARTIKEL_SET='||vr_artikel.REF_ARTIKEL_SET||', OPT_AUTO_WA_BUCHUNG='||vr_artikel.OPT_AUTO_WA_BUCHUNG||', vr_auf_cfg.OPT_PRODUCTION_ORDER='||vr_auf_cfg.OPT_PRODUCTION_ORDER);
        end if;

        delete from TMP_BES_RES_FEHLER where RES_ID=pID and REF_AUF_POS=vr_auf_pos.REF;

        if (vr_artikel.OPT_AUTO_WA_BUCHUNG = '1') then
          null;
        else
          if (nvl (vr_auf_cfg.OPT_PRODUCTION_ORDER, '0') = '0') or (vr_artikel.REF_ARTIKEL_SET is null) then
            --Bei normalen Aufträgen dürfen vollständige Artikelsets kommissioniert werden
            res := FIND_AUFTRAG_POS_BESTAND (vr_auf, vr_auf_pos, vr_auf_pos_ae, pID, pMHDToleranz, pKommOpt, rPlanArt, v_benoetigt, v_rest, v_besstext, oErrorCode, oErrorText);
          else
            --Bei Produktionsaufträgen muss die Stückliste aufgelöst werden
            v_rest := v_benoetigt;
          end if;

          --Bei Stücklisten
          if (res = 0) and (v_rest > 0) and (vr_artikel.REF_ARTIKEL_SET is not null) then
            if (lTraceLevel >= 5) then
              BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_rest='||v_rest);
            end if;

            declare
              vr_set          ARTIKEL_SET%ROWTYPE;
              vr_set_ar       ARTIKEL%ROWTYPE;
              vr_set_ae       ARTIKEL_EINHEIT%ROWTYPE;
              v_set_using     PLS_INTEGER;
              v_set_res       PLS_INTEGER;
              v_set_pick      PLS_INTEGER;
              v_set_available PLS_INTEGER;
              v_set_pos_use   PLS_INTEGER;
              v_set_pos_rest  PLS_INTEGER;
              v_set_pos_pick  PLS_INTEGER;
              v_set_pos_res   PLS_INTEGER;
              v_set_res_id    LAGER_RES_BESTAND.RES_ID%TYPE;
              v_loop_ok       boolean;
            begin
              select * into vr_set from ARTIKEL_SET where REF=vr_artikel.REF_ARTIKEL_SET;

              if (lTraceLevel >= 5) then
                BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_cfg.OPT_PRODUCTION_ORDER='||vr_auf_cfg.OPT_PRODUCTION_ORDER||', vr_set: OPT_KOMM_NO_SPLIT='||vr_set.OPT_KOMM_NO_SPLIT||', OPT_PRODUCTION_SET='||vr_set.OPT_PRODUCTION_SET);
              end if;

              --Nur bei Produktionsaufträgen dürfen Produktionsstücklisten aufgelöst werden
              if (nvl (vr_auf_cfg.OPT_PRODUCTION_ORDER, '0') = '1') or ((nvl (vr_set.OPT_KOMM_NO_SPLIT, '0') = '0') and ((nvl (vr_set.OPT_PRODUCTION_SET, '0') = '0'))) then
                /*
                if (pID is null) then
                  null;
                else
                  --Ermitteln, wie viel ganze Sets bereits reserviert wurde
                  select sum (MENGE_RES) into v_set_res from LAGER_RES_BESTAND where REF_AUF_POS=vr_auf_pos.REF and USED_AR_EINHEIT=vr_auf_pos_ae.REF;

                  --Prüfen, wie viele ganze Sets davon bereits gepickt wurden
                  select sum (MENGE_PICK) into v_set_pick from AUFTRAG_KOMM_POS where REF_AUF_POS=vr_auf_pos.REF and REF_AR_EINHEIT_PICK=vr_auf_pos_ae.REF;

                  if (lTraceLevel >= 5) then
                    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_res='||v_set_res||', v_set_pick='||v_set_pick);
                  end if;

                  v_set_using := vr_auf_pos.MENGE_SOLL - nvl (v_set_pick, 0) - nvl (v_set_res, 0);

                  if (v_set_using > 0) then
                    select SEQ_ID.NEXTVAL into v_set_res_id from dual;
                    insert into TMP_PLAN_RES_ID (RES_ID) values (v_set_res_id);

                    if (lTraceLevel >= 5) then
                      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_res_id='||v_set_res_id);
                    end if;

                    loop
                      v_loop_ok := True;

                      v_set_available := v_set_using;

                      if (lTraceLevel >= 5) then
                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_using='||v_set_using);
                      end if;

                      --Die einzelnen Positionen aus der Stückliste planen
                      for cr_set_ar in (select * from ARTIKEL_SET_POS where REF_SET=vr_artikel.REF_ARTIKEL_SET and nvl (ANZAHL, 0) > 0 order by ANZAHL desc) loop
                        select * into vr_set_ae from ARTIKEL_EINHEIT where REF=cr_set_ar.REF_AR_EINHEIT;
                        select * into vr_set_ar from ARTIKEL where REF=vr_set_ae.REF_AR;

                        if (lTraceLevel >= 5) then
                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_set_ar : ANZAHL='||cr_set_ar.ANZAHL||', BRUCH_FAKTOR='||cr_set_ar.BRUCH_FAKTOR||', vr_set_ae.REF='||vr_set_ae.REF||', vr_set_ar.REF='||vr_set_ar.REF);
                        end if;

                        if (nvl (cr_set_ar.ANZAHL, 0) > 0) then
                          --Prüfen, ob ein Bruchfaktor definiert ist
                          if (nvl (cr_set_ar.BRUCH_FAKTOR, 0.0) > 0) then
                            --Es wird immer eine ganzzahlige Menge benötigt
                            v_set_pos_use := ceil (v_set_using * cr_set_ar.ANZAHL + (v_set_using * cr_set_ar.ANZAHL * (cr_set_ar.BRUCH_FAKTOR / 100.0)));
                          else
                            --Es wird immer eine ganzzahlige Menge benötigt
                            v_set_pos_use := ceil (v_set_using * cr_set_ar.ANZAHL);
                          end if;

                          if (lTraceLevel >= 5) then
                            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_pos_use='||v_set_pos_use);
                          end if;

                          --Ermitteln, wie viel bereits reserviert wurde
                          select sum (nvl (MENGE_RES, 0) - nvl (MENGE_KOMM, 0)) into v_set_pos_res from LAGER_RES_BESTAND res, ARTIKEL_EINHEIT ae where ae.REF=USED_AR_EINHEIT and res.REF_AUF_POS=vr_auf_pos.REF and ae.REF_AR=vr_set_ae.REF_AR and ae.REF_EINHEIT=vr_set_ae.REF_EINHEIT;

                          --Prüfen, wie viele Teile davon bereits gepickt wurden
                          select sum (MENGE_PICK) into v_set_pos_pick from AUFTRAG_KOMM_POS akp, ARTIKEL_EINHEIT ae where ae.REF=akp.REF_AR_EINHEIT_PICK and akp.REF_AUF_POS=vr_auf_pos.REF and  ae.REF_AR=vr_set_ae.REF_AR and ae.REF_EINHEIT=vr_set_ae.REF_EINHEIT;

                          if (lTraceLevel >= 5) then
                            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_pos_res='||v_set_pos_res||', v_set_pos_pick='||v_set_pos_pick);
                          end if;

                          v_set_pos_use := v_set_pos_use - nvl (v_set_pos_pick, 0) - nvl (v_set_pos_res, 0);

                          if (v_set_pos_use > 0) then
                            res := FIND_AUFTRAG_POS_BESTAND (vr_auf, vr_auf_pos, vr_set_ae, v_set_res_id, pMHDToleranz, pKommOpt, rPlanArt, v_set_pos_use, v_set_pos_rest, v_besstext, oErrorCode, oErrorText);

                            exit when (res <> 0) or (oErrorCode <> 0);
                          end if;

                          if (lTraceLevel >= 5) then
                            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_pos_rest='||v_set_pos_rest);
                          end if;

                          --Wenn die geforderte Menge nicht erreicht wird
                          if (v_set_pos_rest > 0) then
                            --Das ganze nochmals über alle Set-Positionen
                            v_loop_ok := False;

                            InsertBestandsfehler (v_set_res_id, vr_set_ar, vr_set_ae, v_set_pos_use, v_set_pos_rest, v_besstext);

                            update TMP_BES_RES_FEHLER set RES_ID=pID where RES_ID=v_set_res_id;

                            --Wenn nicht mal ein Set erfüllt werden kann
                            if (v_set_pos_rest < cr_set_ar.ANZAHL) then
                              v_set_available := 0;
                            else
                              --Die maximal möglich Menge anpassen
                              if (v_set_using - (v_set_pos_rest / cr_set_ar.ANZAHL) < v_set_available) then
                                v_set_available := v_set_using - (v_set_pos_rest / cr_set_ar.ANZAHL);
                              end if;
                            end if;

                            if (lTraceLevel >= 5) then
                              BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_available='||v_set_available);
                            end if;
                          end if;
                        end if;

                        --Geht es so oder so mehr weiter
                        exit when (v_set_available <= 0);
                      end loop;

                      if (lTraceLevel >= 5) then
                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_loop_ok='||case when v_loop_ok then 'true' when not v_loop_ok then 'false' else 'null' end||', v_set_available='||v_set_available);
                      end if;

                      --Wenn die geforderte Gesamtmenge nicht erreicht wurde
                      if not (v_loop_ok) then
                        --Alle Set-Reservierungen wieder verwerfen
                        res := RESET_RESERVIERUNG (v_set_res_id);

                        --Wenn nicht mindestens ein Set kommissioniert werden kann
                        if (v_set_available <= 0) then
                          --Ist hier Schluss
                          exit;
                        else
                          --Mit der maximal möglichen Menge nochmals planen
                          v_set_using := v_set_available;
                        end if;
                      end if;

                      exit when (res <> 0) or (oErrorCode <> 0);

                      --Wenn nicht mindestens ein Set kommissioniert werden kann
                      exit when (v_set_using <= 0);

                      --Wenn für alle Set-Positionen eine Reservierung möglich war
                      exit when v_loop_ok;
                    end loop;

                    if (res = 0) then
                      --RES_ID der Set-Reservierungen umsetzen
                      if (v_set_using > 0) then
                        v_rest := v_rest - v_set_using;

                        update LAGER_RES_BESTAND set RES_ID=pID, SET_RES_COUNT=v_set_using where RES_ID=v_set_res_id;

                        --Gilt auch für die Temptabelle
                        update TMP_KOMM_RES set RES_ID=pID where RES_ID=v_set_res_id;

                        v_set_res_id := pID;
                      end if;
                    end if;
                  end if;
                end if;
                  */

                --Ermitteln, wie viel ganze Sets bereits reserviert wurde
                select sum (MENGE_RES) into v_set_res from LAGER_RES_BESTAND where REF_AUF_POS=vr_auf_pos.REF and USED_AR_EINHEIT=vr_auf_pos_ae.REF;

                --Prüfen, wie viele ganze Sets davon bereits gepickt wurden
                select sum (MENGE_PICK) into v_set_pick from AUFTRAG_KOMM_POS where REF_AUF_POS=vr_auf_pos.REF and REF_AR_EINHEIT_PICK=vr_auf_pos_ae.REF;

                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_res='||v_set_res||', v_set_pick='||v_set_pick);

                v_set_using := vr_auf_pos.MENGE_SOLL - nvl (v_set_pick, 0) - nvl (v_set_res, 0);

                if (v_set_using > 0) then
                  if (pID is null) then
                    v_set_res_id := null;
                  else
                    select SEQ_ID.NEXTVAL into v_set_res_id from dual;
                    insert into TMP_PLAN_RES_ID (RES_ID) values (v_set_res_id);
                  end if;

                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_res_id='||v_set_res_id);

                  loop
                    v_loop_ok := True;

                    v_set_available := v_set_using;

                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_using='||v_set_using);

                    --Die einzelnen Positionen aus der Stückliste planen
                    for cr_set_ar in (select * from ARTIKEL_SET_POS where REF_SET=vr_artikel.REF_ARTIKEL_SET and nvl (ANZAHL, 0) > 0 order by ANZAHL desc) loop
                      select * into vr_set_ae from ARTIKEL_EINHEIT where REF=cr_set_ar.REF_AR_EINHEIT;
                      select * into vr_set_ar from ARTIKEL where REF=vr_set_ae.REF_AR;

                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_set_ar : ANZAHL='||cr_set_ar.ANZAHL||', BRUCH_FAKTOR='||cr_set_ar.BRUCH_FAKTOR||', vr_set_ae.REF='||vr_set_ae.REF||', vr_set_ar.REF='||vr_set_ar.REF);

                      if (nvl (cr_set_ar.ANZAHL, 0) > 0) then
                        --Prüfen, ob ein Bruchfaktor definiert ist
                        if (nvl (cr_set_ar.BRUCH_FAKTOR, 0.0) > 0) then
                          --Es wird immer eine ganzzahlige Menge benötigt
                          v_set_pos_use := ceil (v_set_using * cr_set_ar.ANZAHL + (v_set_using * cr_set_ar.ANZAHL * (cr_set_ar.BRUCH_FAKTOR / 100.0)));
                        else
                          --Es wird immer eine ganzzahlige Menge benötigt
                          v_set_pos_use := ceil (v_set_using * cr_set_ar.ANZAHL);
                        end if;

                        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_pos_use='||v_set_pos_use);

                        --Ermitteln, wie viel bereits reserviert wurde
                        select sum (nvl (MENGE_RES, 0) - nvl (MENGE_KOMM, 0)) into v_set_pos_res from LAGER_RES_BESTAND res, ARTIKEL_EINHEIT ae where ae.REF=USED_AR_EINHEIT and res.REF_AUF_POS=vr_auf_pos.REF and ae.REF_AR=vr_set_ae.REF_AR and ae.REF_EINHEIT=vr_set_ae.REF_EINHEIT;

                        --Prüfen, wie viele Teile davon bereits gepickt wurden
                        select sum (MENGE_PICK) into v_set_pos_pick from AUFTRAG_KOMM_POS akp, ARTIKEL_EINHEIT ae where ae.REF=akp.REF_AR_EINHEIT_PICK and akp.REF_AUF_POS=vr_auf_pos.REF and  ae.REF_AR=vr_set_ae.REF_AR and ae.REF_EINHEIT=vr_set_ae.REF_EINHEIT;

                        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_pos_res='||v_set_pos_res||', v_set_pos_pick='||v_set_pos_pick);

                        v_set_pos_use := v_set_pos_use - nvl (v_set_pos_pick, 0) - nvl (v_set_pos_res, 0);

                        if (v_set_pos_use > 0) then
                          res := FIND_AUFTRAG_POS_BESTAND (vr_auf, vr_auf_pos, vr_set_ae, v_set_res_id, pMHDToleranz, pKommOpt, rPlanArt, v_set_pos_use, v_set_pos_rest, v_besstext, oErrorCode, oErrorText);

                          exit when (res <> 0) or (oErrorCode <> 0);
                        end if;

                        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_pos_rest='||v_set_pos_rest);

                        --Wenn die geforderte Menge nicht erreicht wird
                        if (v_set_pos_rest > 0) then
                          --Das ganze nochmals über alle Set-Positionen
                          v_loop_ok := False;

                          InsertBestandsfehler (v_set_res_id, vr_set_ar, vr_set_ae, v_set_pos_use, v_set_pos_rest, v_besstext);

                          if (pID is not null) then
                            update TMP_BES_RES_FEHLER set RES_ID=pID where RES_ID=v_set_res_id;
                          end if;

                          --Wenn nicht mal ein Set erfüllt werden kann
                          if (v_set_pos_rest < cr_set_ar.ANZAHL) then
                            v_set_available := 0;
                          else
                            --Die maximal möglich Menge anpassen
                            if (v_set_using - (v_set_pos_rest / cr_set_ar.ANZAHL) < v_set_available) then
                              v_set_available := v_set_using - (v_set_pos_rest / cr_set_ar.ANZAHL);
                            end if;
                          end if;

                          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_available='||v_set_available);
                        end if;
                      end if;

                      --Geht es so oder so mehr weiter
                      exit when (v_set_available <= 0);

                      res := PA_TIMEOUT.CheckTimeout (TRUE);
                    end loop;

                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_loop_ok='||case when v_loop_ok then 'true' when not v_loop_ok then 'false' else 'null' end||', v_set_available='||v_set_available);

                    --Wenn die geforderte Gesamtmenge nicht erreicht wurde
                    if not (v_loop_ok) then
                      if (v_set_res_id is not null) then
                        --Alle Set-Reservierungen wieder verwerfen
                        res := RESET_RESERVIERUNG (v_set_res_id);
                      end if;

                      --Wenn nicht mindestens ein Set kommissioniert werden kann
                      if (v_set_available <= 0) then
                        --Ist hier Schluss
                        exit;
                      else
                        --Mit der maximal möglichen Menge nochmals planen
                        v_set_using := v_set_available;
                      end if;
                    end if;

                    exit when (res <> 0) or (oErrorCode <> 0);

                    --Wenn nicht mindestens ein Set kommissioniert werden kann
                    exit when (v_set_using <= 0);

                    --Wenn für alle Set-Positionen eine Reservierung möglich war
                    exit when v_loop_ok;

                    res := PA_TIMEOUT.CheckTimeout (TRUE);
                  end loop;

                  if (res = 0) then
                    --RES_ID der Set-Reservierungen umsetzen
                    if (v_set_using > 0) then
                      v_rest := v_rest - v_set_using;

                      if (pID is not null) then
                        update LAGER_RES_BESTAND set RES_ID=pID, SET_RES_COUNT=v_set_using where RES_ID=v_set_res_id;

                        --Gilt auch für die Temptabelle
                        update TMP_KOMM_RES set RES_ID=pID where RES_ID=v_set_res_id;

                        v_set_res_id := pID;
                      end if;
                    end if;
                  end if;
                end if;
              end if;

              if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': set v_rest='||v_rest); end if;

              if (v_rest > 0) then
                InsertBestandsfehler (v_set_res_id, vr_artikel, vr_auf_pos_ae, v_benoetigt, v_rest, v_besstext);
              end if;
            end;
          else
            if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_rest='||v_rest); end if;

            if (v_rest > 0) then
              InsertBestandsfehler (pID, vr_artikel, vr_auf_pos_ae, v_benoetigt, v_rest, v_besstext);
            end if;
          end if;
        end if;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oFehlerStatus='||oFehlerStatus);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_BESTAND (prAuftrag    in  AUFTRAG%ROWTYPE,
                        pID          in  LAGER_RES_BESTAND.RES_ID%TYPE,
                        pMHDToleranz in  INTEGER,
                        pKommOpt     in  varchar2,
                        rPlanArt     in  AUFTRAG_ART_PLANUNG%rowtype,
                        rBatchConfig in  BATCHLAUF_CONFIG%rowtype,
                        oOk          OUT INTEGER,
                        oErrorCode   OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel        PLS_INTEGER;

	vr_mandant      MANDANT%ROWTYPE;
  vr_auf_cfg      AUFTRAG_ART_CONFIG%ROWTYPE;
  vr_auf_pos      AUFTRAG_POS%ROWTYPE;
  vr_nve          LAGER_NVE%ROWTYPE;
  vr_auf_plan     AUFTRAG_ART_PLANUNG%rowtype;

  v_stat          FEHLER_LISTE.FEHLER_STATUS%TYPE;
  v_ref_err       FEHLER_LISTE.REF%TYPE;
  v_ref_err_pack  FEHLER_LISTE.REF%TYPE;
  v_ref_err_batch FEHLER_LISTE.REF%TYPE;
  v_ref_err_pos   FEHLER_POS.REF%TYPE;

  v_tab           PLS_INTEGER;

  v_count         INTEGER;

  res	            NUMBER;

begin
  res := 0;

  FehlerTab.delete;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_BESTAND');
  BASE_TRACING.TraceOutput (2,'prAuftrag.REF='||prAuftrag.REF);
  BASE_TRACING.TraceOutput (2,'pID          ='||pID);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz ='||pMHDToleranz);
  BASE_TRACING.TraceOutput (2,'pKommOpt     ='||pKommOpt);

  oOk := 1;

  update AUFTRAG_TIMING set CHECK_BESTAND=sysdate where REF_AUF_KOPF=prAuftrag.REF;

  if (prAuftrag.REF_ART_CONFIG is not null) then
    select * into vr_auf_cfg from AUFTRAG_ART_CONFIG where REF=prAuftrag.REF_ART_CONFIG;
  else
    vr_auf_cfg := null;
  end if;

  if (rPlanArt.REF is not null) then
    vr_auf_plan := rPlanArt;
  elsif (prAuftrag.REF_ART_PLANUNG is not null) then
    select * into vr_auf_plan from AUFTRAG_ART_PLANUNG where REF=prAuftrag.REF_ART_PLANUNG;
  else
    begin
      select * into vr_auf_plan from (select * from AUFTRAG_ART_PLANUNG where REF_MAND=prAuftrag.REF_MAND and (REF_SUB_MAND is null or (nvl (REF_SUB_MAND,-1)=nvl (prAuftrag.REF_SUB_MAND, -1))) and REF_LAGER=prAuftrag.REF_LAGER and (AUFTRAG_ART is null or AUFTRAG_ART=prAuftrag.AUFTRAGSART) order by AUFTRAG_ART nulls last, REF_SUB_MAND nulls last) where ROWNUM=1;

      exception
        when no_data_found then
          vr_auf_plan := null;
        when others then
          raise;
    end;
  end if;

  if not (pID is null) then
    --Die Vorreservierung zurücksetzen
    for cr_vor in (select * from LAGER_VOR_RES_BESTAND where REF_AUF_POS in (select REF from AUFTRAG_POS where REF_AUF_KOPF=prAuftrag.REF)) loop
      declare
        vr_bres LAGER_BESTAND_SUMME_RES%rowtype;
      begin
        if (lTraceLevel >= 5) then BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'> cr_vor: REF='||cr_vor.REF||', REF_AUF_POS='||cr_vor.REF_AUF_POS||', MENGE_RES='||cr_vor.MENGE_RES||', REF_BES_SUM='||cr_vor.REF_BES_SUM); end if;

        insert into TMP_VOR_RES
            (RES_ID, REF_VOR_RES, REF_AUF_POS, MENGE_RES, REF_BES_SUM)
          values
            (pID, cr_vor.REF, cr_vor.REF_AUF_POS, cr_vor.MENGE_RES, cr_vor.REF_BES_SUM);

        if (lTraceLevel >= 5) then
          select * into vr_bres from LAGER_BESTAND_SUMME_RES where REF_BES_SUM=cr_vor.REF_BES_SUM;

          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bres.MENGE_VOR_RES='||vr_bres.MENGE_VOR_RES);
        end if;

        UPDATE LAGER_BESTAND_SUMME_RES
          SET
            MENGE_VOR_RES= case
                             when (nvl (MENGE_VOR_RES, 0) > cr_vor.MENGE_RES) then
                               (nvl (MENGE_VOR_RES, 0) - cr_vor.MENGE_RES)
                             else
                               (null)
                           end
          WHERE
             REF_BES_SUM=cr_vor.REF_BES_SUM;

        update LAGER_VOR_RES_BESTAND set MENGE_RES=0 where REF=cr_vor.REF;
      end;
    end loop;
  end if;

  update AUFTRAG set FEHLER_STATUS=null where REF=prAuftrag.REF;

  delete from FEHLER_LISTE where REF_ID=prAuftrag.REF;

  if (prAuftrag.REF_PACKLISTE is not null) then
    delete from FEHLER_POS where REF_LISTE in (select REF_FEHLER from AUFTRAG_PACKLISTE where REF=prAuftrag.REF_PACKLISTE) and REF_ID in (select REF from AUFTRAG_POS where REF_AUF_KOPF=prAuftrag.REF);
  end if;


  v_stat := null;

	select * into vr_mandant from MANDANT where REF=prAuftrag.REF_MAND;

	if (oErrorCode = 0) and (res = 0) then
    open c_auftrag_pos (prAuftrag.REF);
    begin
  		loop
        fetch c_auftrag_pos into vr_auf_pos;
        exit when c_auftrag_pos%NOTFOUND;

        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_pos: POS_NR='||vr_auf_pos.POS_NR||', CROSSDOCK_BESTELL_NR='||vr_auf_pos.CROSSDOCK_BESTELL_NR||', SET_POSITION='||vr_auf_pos.SET_POSITION||', TEXT_POSITION='||vr_auf_pos.TEXT_POSITION);

        if (vr_auf_pos.CROSSDOCK_BESTELL_NR is null) and (nvl (vr_auf_pos.SET_POSITION, '0') = '0') and (nvl (vr_auf_pos.TEXT_POSITION, '0') = '0') then
          if (nvl (rBatchConfig.OPT_TEIL_PLANUNG, '0') = '0') then
            res := CHECK_AUFTRAG_POS_BESTAND (prAuftrag, vr_auf_cfg, vr_auf_pos, pID, pMHDToleranz, pKommOpt, vr_auf_plan, v_stat, oErrorCode, oErrorText);
          else
            declare
              v_ok        BOOLEAN;
              v_pos_count PLS_INTEGER;
            begin

              if (nvl (rBatchConfig.OPT_SET_PRE_PICK, '0') = '1') then
                v_ok := false;

                BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_pos.REF_AR='||vr_auf_pos.REF_AR);

                for cr_set in (select s.REF,(select count (*) from ARTIKEL_SET_POS where REF_SET=s.REF and nvl (ANZAHL, 0) > 0) as SET_ANZ from ARTIKEL_SET s where nvl (s.OPT_PRE_PICK_RUN, '0')='1' and s.REF in (select REF_SET from ARTIKEL_SET_POS where nvl (ANZAHL, 0) > 0 and REF_AR_EINHEIT in (select REF from ARTIKEL_EINHEIT where REF_AR=vr_auf_pos.REF_AR))) loop
                  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_set : REF='||cr_set.REF||', SET_ANZ='||cr_set.SET_ANZ);

                  --Prüfen, ob alle Setpositionen im Auftag auch vorkommen
                  select count (*) into v_pos_count from AUFTRAG_POS where REF_AUF_KOPF=prAuftrag.REF and REF_AR in (select REF_AR from ARTIKEL_EINHEIT where REF in (select REF_AR_EINHEIT from ARTIKEL_SET_POS where REF_SET=cr_set.REF and nvl (ANZAHL, 0) > 0));

                  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_pos_count='||v_pos_count);

                  --Wenn ja, kann die Position geplant werden
                  if (v_pos_count = cr_set.SET_ANZ) then
                    v_ok := true;
                    exit;
                  end if;
                end loop;
              else
                declare
                  v_big   ARTIKEL_EINHEIT.OPT_BIG_ITEM%type;
                  v_sperr ARTIKEL_EINHEIT.OPT_SPERRGUT%type;
                begin
                  v_ok := true;

                  select
                      coalesce (ael.OPT_BIG_ITEM, ae.OPT_BIG_ITEM, '0'), coalesce (ael.OPT_SPERRGUT, ae.OPT_SPERRGUT, '0')
                    into
                      v_big, v_sperr
                    from
                      ARTIKEL_EINHEIT ae
                      left outer join ARTIKEL_EINHEIT_REL_LAGER ael on (ael.REF_AR_EINHEIT=ae.REF and REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=prAuftrag.REF_LAGER))
                    where
                      ae.REF=vr_auf_pos.REF_AR_EINHEIT;

                  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': rBatchConfig: OPT_BIG_ITEM='||rBatchConfig.OPT_BIG_ITEM||', OPT_SPERRGUT='||rBatchConfig.OPT_SPERRGUT||', v_big='||v_big||', v_sperr='||v_sperr);

                  if (nvl (rBatchConfig.OPT_BIG_ITEM, '0') = '1') then
                    if (nvl (v_big, '0') <> '1') and (nvl (rBatchConfig.OPT_BIG_ITEM_ONLY, '0') = '1') then
                      v_ok := false;
                    end if;
                  end if;

                  if v_ok and (nvl (rBatchConfig.OPT_SPERRGUT, '0') = '1') then
                    if (nvl (v_sperr, '0') <> '1') and (nvl (rBatchConfig.OPT_SPERRGUT_ONLY, '0') = '1') then
                      v_ok := false;
                    end if;
                  end if;

                  if v_ok then
                    res := CHECK_AUFTRAG_POS_BESTAND (prAuftrag, vr_auf_cfg, vr_auf_pos, pID, pMHDToleranz, pKommOpt, vr_auf_plan, v_stat, oErrorCode, oErrorText);
                  end if;
                end;
              end if;
            end;
          end if;
        end if;

        exit when (res <> 0) or (oErrorCode <> 0);
  		end loop;
  		close c_auftrag_pos;

      EXCEPTION WHEN OTHERS THEN
    		close c_auftrag_pos;

        raise;
    end;
  end if;

  if (res = 0) then
    for cr_auf_pos in (select * from AUFTRAG_POS_LT where REF_AUF_KOPF=prAuftrag.REF) loop
      if (cr_auf_pos.NVE_NR is not null and nvl (cr_auf_pos.LT_ANZ_SOLL, 0) > nvl (cr_auf_pos.LT_ANZ_GEPLANT, 0)) then
        select * into vr_auf_pos from AUFTRAG_POS where REF=cr_auf_pos.REF_AUF_POS;

        begin
          select
              * into vr_nve
            from
              LAGER_NVE
            where
              REF_MAND=prAuftrag.REF_MAND and
              ((REF_SUB_MAND is null and prAuftrag.REF_SUB_MAND is null) or (REF_SUB_MAND=REF_SUB_MAND)) and
              NVE_NR=cr_auf_pos.NVE_NR;
          exception
            when no_data_found then
              vr_nve := null;
            when others then
              raise;
        end;

        if (vr_nve.REF is null) then
          v_stat := 'NVE';

          v_tab := FehlerTab.last;
          if (v_tab is NULL) Then v_tab := 1; else v_tab := v_tab + 1; end if;

          FehlerTab (v_tab).RefLTPos  := cr_auf_pos.REF;
          FehlerTab (v_tab).RefKopf   := vr_auf_pos.REF_AUF_KOPF;
          FehlerTab (v_tab).PosNr     := vr_auf_pos.POS_NR;
          FehlerTab (v_tab).Status    := 'NVE';
          FehlerTab (v_tab).Text      := 'NVE nicht vorhanden';
          FehlerTab (v_tab).SollMenge := 1;
          FehlerTab (v_tab).FehlMenge := 1;
        elsif (vr_nve.STATUS = 'DEL') then
          v_stat := 'NVE';

          v_tab := FehlerTab.last;
          if (v_tab is NULL) Then v_tab := 1; else v_tab := v_tab + 1; end if;

          FehlerTab (v_tab).RefLTPos  := cr_auf_pos.REF;
          FehlerTab (v_tab).RefKopf   := vr_auf_pos.REF_AUF_KOPF;
          FehlerTab (v_tab).PosNr     := vr_auf_pos.POS_NR;
          FehlerTab (v_tab).Status    := 'NVE';
          FehlerTab (v_tab).Text      := 'NVE ist gelöscht';
          FehlerTab (v_tab).SollMenge := 1;
          FehlerTab (v_tab).FehlMenge := 1;
        elsif (vr_nve.REF_LAGER is null) then
          v_stat := 'NVE';

          v_tab := FehlerTab.last;
          if (v_tab is NULL) Then v_tab := 1; else v_tab := v_tab + 1; end if;

          FehlerTab (v_tab).RefLTPos  := cr_auf_pos.REF;
          FehlerTab (v_tab).RefKopf   := vr_auf_pos.REF_AUF_KOPF;
          FehlerTab (v_tab).PosNr     := vr_auf_pos.POS_NR;
          FehlerTab (v_tab).Status    := 'NVE';
          FehlerTab (v_tab).Text      := 'NVE wurde bereits ausgelagert';
          FehlerTab (v_tab).SollMenge := 1;
          FehlerTab (v_tab).FehlMenge := 1;
        elsif (vr_nve.REF_LAGER<>prAuftrag.REF_LAGER) then
          v_stat := 'NVE';

          v_tab := FehlerTab.last;
          if (v_tab is NULL) Then v_tab := 1; else v_tab := v_tab + 1; end if;

          FehlerTab (v_tab).RefLTPos  := cr_auf_pos.REF;
          FehlerTab (v_tab).RefKopf   := vr_auf_pos.REF_AUF_KOPF;
          FehlerTab (v_tab).PosNr     := vr_auf_pos.POS_NR;
          FehlerTab (v_tab).Status    := 'NVE';
          FehlerTab (v_tab).Text      := 'NVE steht in einem anderen Lager';
          FehlerTab (v_tab).SollMenge := 1;
          FehlerTab (v_tab).FehlMenge := 1;
        else
          if (pID is null) then
            null;
          else
            insert into LAGER_RES_BESTAND (RES_ID, REF_AUF_POS, REF_AUF_POS_LT, REF_NVE, MENGE_PLAN) values (pID, vr_auf_pos.REF, cr_auf_pos.REF, vr_nve.REF, cr_auf_pos.LT_ANZ_SOLL);
          end if;

          update AUFTRAG_POS_LT set LT_ANZ_GEPLANT=cr_auf_pos.LT_ANZ_SOLL, REF_NVE=vr_nve.REF where REF=cr_auf_pos.REF;
        end if;
      end if;
    end loop;
  end if;


  if (res = 0) then
    v_tab := FehlerTab.FIRST;

    if (v_tab is NULL) Then
      oOk := 1;

      if (prAuftrag.REF_PACKLISTE is not null) then
        --Prüfen ob es noch Fehler-Einträge gibt, die sich auf die Packliste beziehen
        select count (*) into v_count from FEHLER_POS where REF_LISTE in (select REF_FEHLER from AUFTRAG_PACKLISTE where REF=prAuftrag.REF_PACKLISTE);

        if (v_count = 0) then
          --Wenn nicht wird der Fehlerstatus der Packlist zurückgesetzt
          delete FEHLER_LISTE where REF in (select REF_FEHLER from AUFTRAG_PACKLISTE where REF=prAuftrag.REF_PACKLISTE);

          update AUFTRAG_PACKLISTE set FEHLER_STATUS=null where REF=prAuftrag.REF_PACKLISTE;
        end if;
      end if;
    else
      oOk := 0;

      select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (1,2,3);

      if (v_count > 0) then
        v_stat := 'EINLAGER';
      else
        select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (8);

        if (v_count > 0) then
          v_stat := 'INVENTUR';
        else
          select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (6);

          if (v_count > 0) then
            v_stat := 'BEREICH';
          else
            select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (32, 33);

            if (v_count > 0) then
              v_stat := 'WE-OFFEN';
            else
              select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (15,16);

              if (v_count > 0) then
                v_stat := 'UMLAGERN';
              else
                select count (*) into v_count from TMP_BES_RES_FEHLER where FEHLER_CODE in (14,17,18);

                if (v_count > 0) then
                  v_stat := 'NACHSCHUB';
                else
                  v_stat := 'BESTAND';
                end if;
              end if;
            end if;
          end if;
        end if;
      end if;

      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': Fehlerliste');

      select REF_FEHLER into v_ref_err from AUFTRAG where REF=prAuftrag.REF;

      begin
        select REF into v_ref_err from FEHLER_LISTE where REF=v_ref_err;

        exception
          when no_data_found then
            v_ref_err := null;

          when others then
            raise;
      end;
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_ref_err='||v_ref_err);

      if (v_ref_err is null) then
        insert into FEHLER_LISTE
            (REF_ID, FEHLER_STATUS, FEHLER_TEXT,ID_TYP)
          values
            (prAuftrag.REF, v_stat, oErrorText,'AUF')
          returning REF into v_ref_err;
      end if;

      update AUFTRAG set FEHLER_STATUS=v_stat, REF_FEHLER=v_ref_err where REF=prAuftrag.REF;

      if (prAuftrag.REF_PACKLISTE is null) then
        v_ref_err_pack := null;
      else
        select REF_FEHLER into v_ref_err_pack from AUFTRAG_PACKLISTE where REF=prAuftrag.REF_PACKLISTE;

        begin
          select REF into v_ref_err_pack from FEHLER_LISTE where REF=v_ref_err_pack;

          exception
            when no_data_found then
              v_ref_err_pack := null;

            when others then
              raise;
        end;
        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_ref_err_pack='||v_ref_err_pack);

        if (v_ref_err_pack is null) then
          insert into FEHLER_LISTE
              (REF_ID, FEHLER_STATUS, FEHLER_TEXT, ID_TYP)
            values
              (prAuftrag.REF_PACKLISTE, v_stat, oErrorText,'PACK')
            returning REF into v_ref_err_pack;
        end if;

        update AUFTRAG_PACKLISTE set FEHLER_STATUS=v_stat, REF_FEHLER=v_ref_err_pack where REF=prAuftrag.REF_PACKLISTE;
      end if;

      --Fehlerliste für den Batchlauf
      if (prAuftrag.REF_BATCHLAUF is null) then
        v_ref_err_batch := null;
      else
        select REF_FEHLER into v_ref_err_batch from AUFTRAG_BATCHLAUF where REF=prAuftrag.REF_BATCHLAUF;

        begin
          select REF into v_ref_err_batch from FEHLER_LISTE where REF=v_ref_err_batch;

          exception
            when no_data_found then
              v_ref_err_batch := null;

            when others then
              raise;
        end;
        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_ref_err_batch='||v_ref_err_batch);

        if (v_ref_err_batch is null) then
          insert into FEHLER_LISTE
              (REF_ID, FEHLER_STATUS, FEHLER_TEXT, ID_TYP)
            values
              (prAuftrag.REF_BATCHLAUF, v_stat, oErrorText,'BATCH')
            returning REF into v_ref_err_batch;
        end if;

        update AUFTRAG_BATCHLAUF set FEHLER_STATUS=v_stat, REF_FEHLER=v_ref_err_batch where REF=prAuftrag.REF_BATCHLAUF;
      end if;

      loop
        declare
          v_parent AUFTRAG_POS.REF_PARENT_POS%TYPE;
        begin
          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': FehlerTab (v_tab): PosNr='||FehlerTab (v_tab).PosNr||', RefPos='||FehlerTab (v_tab).RefPos||', Status='||FehlerTab (v_tab).Status||', RefLTPos='||FehlerTab (v_tab).RefLTPos);

          if (FehlerTab (v_tab).RefLTPos is not null) then
            insert into FEHLER_POS
                (REF_LISTE, REF_ID, REF_POS, REF_AR_EINHEIT, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
              values
                (v_ref_err, FehlerTab (v_tab).RefLTPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).RefArEinheit, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht)
              returning REF into v_ref_err_pos;

            update AUFTRAG_POS_LT set REF_FEHLER_POS=v_ref_err_pos where REF=FehlerTab (v_tab).RefLTPos;
          else
            insert into FEHLER_POS
                (REF_LISTE, REF_ID, REF_POS, REF_AR_EINHEIT, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
              values
                (v_ref_err, FehlerTab (v_tab).RefPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).RefArEinheit, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht)
              returning REF into v_ref_err_pos;

            update AUFTRAG_POS set REF_FEHLER_POS=v_ref_err_pos where REF=FehlerTab (v_tab).RefPos returning REF_PARENT_POS into v_parent;
            if (v_parent is not null) then
              update AUFTRAG_POS set REF_FEHLER_POS=v_ref_err_pos where REF=v_parent;
            end if;

            if (v_ref_err_pack is not null) then
              insert into FEHLER_POS
                  (REF_LISTE, REF_ID, REF_POS, REF_AR_EINHEIT, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
                values
                  (v_ref_err_pack, FehlerTab (v_tab).RefPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).RefArEinheit, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht);
            end if;

            if (v_ref_err_batch is not null) then
              insert into FEHLER_POS
                  (REF_LISTE, REF_ID, REF_POS, REF_AR_EINHEIT, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE, FEHL_GEWICHT)
                values
                  (v_ref_err_batch, FehlerTab (v_tab).RefPos, FehlerTab (v_tab).PosNr, FehlerTab (v_tab).RefArEinheit, FehlerTab (v_tab).Status, substr (FehlerTab (v_tab).Text, 1, 256), FehlerTab (v_tab).SollMenge, FehlerTab (v_tab).FehlMenge, FehlerTab (v_tab).Gewicht);
            end if;
          end if;
        end;

        v_tab := FehlerTab.NEXT (v_tab);

        EXIT WHEN v_tab IS NULL;
      end loop;
  	end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oOk='||oOk);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_AUFTRAG_BESTAND (pRef         in AUFTRAG.REF%TYPE,
                                pMHDToleranz in INTEGER,
                                oOk          OUT INTEGER,
                                oErrorCode   OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_auf_kopf AUFTRAG%ROWTYPE;

  v_res_id    LAGER_RES_BESTAND.RES_ID%TYPE;
  v_manag     LAGER.MANAGED_BY%TYPE;

  v_komm_opt  varchar2 (32);

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  lTraceLevel := BASE_TRACING.GETTRACELEVEL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_AUFTRAG_BESTAND');
  BASE_TRACING.TraceOutput (2,'pRef        ='||pRef);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz='||pMHDToleranz);

  select * into vr_auf_kopf from AUFTRAG where REF=pRef;
  select MANAGED_BY into v_manag from LAGER where REF=vr_auf_kopf.REF_LAGER;

  if (v_manag is not null) then
    oErrorCode := 2600;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_auf_kopf.REF_LAGER), v_manag);
  else
    if (vr_auf_kopf.STATUS in ('ANG','FREI','BER','KOM','PLA')) then
      res := CHECK_TOUR (vr_auf_kopf, oOk, oErrorCode, oErrorText);

      if (res = 0) and (oErrorCode = 0) then
        v_res_id := null;

        v_komm_opt := GetKommOpt (vr_auf_kopf);

        SetKommPlanLB (nvl (vr_auf_kopf.REF_SUB_MAND, vr_auf_kopf.REF_MAND), vr_auf_kopf.REF_LAGER, vr_auf_kopf.AUFTRAGSART);

        res := CHECK_BESTAND (vr_auf_kopf, v_res_id, pMHDToleranz, v_komm_opt, null, null, oOk, oErrorCode, oErrorText);

        if (res = 0) then
          --Wenn der Bestand jetzt ok war, kann der Backlog-Eintrag für den Auftrag zurückgesetzt werden
          if (oOk = 1) then
            update AUFTRAG_BACKLOG set NEXT_CHECK=null where REF_AUF_KOPF=vr_auf_kopf.REF;
          end if;

          res := RESET_RESERVIERUNG (v_res_id);
        end if;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oOk='||oOk);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_PACKLISTE_BESTAND (pRef         in AUFTRAG_PACKLISTE.REF%TYPE,
                                  pMHDToleranz in INTEGER,
                                  oOk          OUT INTEGER,
                                  oErrorCode   OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_pack     AUFTRAG_PACKLISTE%ROWTYPE;

  v_res_id    LAGER_RES_BESTAND.RES_ID%TYPE;
  v_manag     LAGER.MANAGED_BY%TYPE;

  v_ok        INTEGER;
  v_komm_opt  VARCHAR2(32);

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  lTraceLevel := BASE_TRACING.GETTRACELEVEL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_PACKLISTE_BESTAND');
  BASE_TRACING.TraceOutput (2,'pRef        ='||pRef);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz='||pMHDToleranz);

  select * into vr_pack from AUFTRAG_PACKLISTE where REF=pRef;

  if (vr_pack.REF_LAGER is null) then
    v_manag := null;
  else
    select MANAGED_BY into v_manag from LAGER where REF=vr_pack.REF_LAGER;
  end if;

  if (v_manag is not null) then
    oErrorCode := 2600;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_pack.REF_LAGER), v_manag);
  else
    if (vr_pack.STATUS in ('ANG','BER','PLA','KOM')) then
      --Referenz auf die Fehlerliste löschen
      update AUFTRAG_PACKLISTE set REF_FEHLER=null, FEHLER_STATUS=null where REF=pRef;

      update AUFTRAG set REF_FEHLER=null, FEHLER_STATUS=null where REF_PACKLISTE=pRef;
      update AUFTRAG_POS set REF_FEHLER_POS=null where REF_FEHLER_POS is not null and REF_AUF_KOPF in (select REF from AUFTRAG where REF_PACKLISTE=pRef);

      delete from FEHLER_LISTE where REF_ID=pRef;
      delete from FEHLER_LISTE where REF_ID in (select REF from AUFTRAG where REF_PACKLISTE=pRef);

      select SEQ_ID.NEXTVAL into v_res_id from dual;
      insert into TMP_PLAN_RES_ID (RES_ID) values (v_res_id);

      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_res_id='||v_res_id);

      oOk := 1;

      for vr_auf in (select * from AUFTRAG where REF_PACKLISTE=pRef) loop
        select MANAGED_BY into v_manag from LAGER where REF=vr_auf.REF_LAGER;

        if (v_manag is not null) then
          oErrorCode := 2600;
          oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_auf.REF_LAGER), v_manag);
        else
          if (vr_auf.STATUS in ('ANG', 'FREI', 'BER', 'KOM', 'PLA')) then
            res := CHECK_TOUR (vr_auf, v_ok, oErrorCode, oErrorText);

            if (res = 0) and (oErrorCode = 0) then
              v_komm_opt := GetKommOpt (vr_auf);

              SetKommPlanLB (nvl (vr_auf.REF_SUB_MAND, vr_auf.REF_MAND), vr_auf.REF_LAGER, vr_auf.AUFTRAGSART);

              res := CHECK_BESTAND (vr_auf, v_res_id, pMHDToleranz, v_komm_opt, null, null, v_ok, oErrorCode, oErrorText);

              if (res = 0) then
                if (v_ok = 0) then
                  oOk := 0;
                else
                  --Wenn der Bestand jetzt ok war, kann der Backlog-Eintrag für den Auftrag zurückgesetzt werden
                  update AUFTRAG_BACKLOG set NEXT_CHECK=null where REF_AUF_KOPF=vr_auf.REF;
                end if;
              end if;
            end if;
          end if;
        end if;

        exit when (res <> 0) or (oErrorCode <> 0);
      end loop;

      if (res = 0) and (oErrorCode = 0) then
        res := RESET_RESERVIERUNG (v_res_id);
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oOk='||oOk);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CHECK_BATCHLAUF_BESTAND (pRefBatch       in  AUFTRAG_BATCHLAUF.REF%TYPE,
                                  pMHDToleranz    in  INTEGER,
                                  oOk             OUT INTEGER,
                                  oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_batch    AUFTRAG_BATCHLAUF%ROWTYPE;
  vr_art_cfg  AUFTRAG_ART_CONFIG%ROWTYPE;

  v_manag     LAGER.MANAGED_BY%TYPE;
  v_res_id    LAGER_RES_BESTAND.RES_ID%TYPE;

  v_tol       INTEGER;

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  lTraceLevel := BASE_TRACING.GETTRACELEVEL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CHECK_BATCHLAUF_BESTAND');
  BASE_TRACING.TraceOutput (2,'pRefBatch      ='||pRefBatch);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz   ='||pMHDToleranz);

  select * into vr_batch from AUFTRAG_BATCHLAUF where REF=pRefBatch;

  if (vr_batch.REF_LAGER is null) then
    v_manag := null;
  else
    select MANAGED_BY into v_manag from LAGER where REF=vr_batch.REF_LAGER;
  end if;

  if (v_manag is not null) then
    oErrorCode := 2600;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_batch.REF_LAGER), v_manag);
  else
    if (vr_batch.STATUS in ('ANG', 'BER', 'KOM', 'PLA')) then
      delete TMP_AUF;

      insert into TMP_AUF (REF_AUF_KOPF) select REF from AUFTRAG where REF_BATCHLAUF=pRefBatch;

      --Referenz auf die Fehlerliste löschen
      update AUFTRAG_BATCHLAUF set FEHLER_STATUS=null where REF=pRefBatch;
      update AUFTRAG set FEHLER_STATUS=null where REF in (select REF_AUF_KOPF from TMP_AUF);

      delete from FEHLER_LISTE where REF_ID=pRefBatch;
      delete from FEHLER_LISTE where REF_ID in (select REF_AUF_KOPF from TMP_AUF);

      select SEQ_ID.NEXTVAL into v_res_id from dual;
      insert into TMP_PLAN_RES_ID (RES_ID) values (v_res_id);

      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_res_id='||v_res_id);

      oOk := 1;

      for vr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from TMP_AUF)) loop
        select * into vr_art_cfg from AUFTRAG_ART_CONFIG where ref = vr_auf.ref_art_config;
        if (pMHDToleranz is null) and nvl(vr_art_cfg.MHD_TOL,0) = 0 then
          v_tol := 0;

          begin
            select MHD_TOL into v_tol from WARENEMPF where REF=(select REF_WARENEMPF from AUFTRAG where REF=vr_auf.REF);

            exception
              when no_data_found then
                v_tol := 0;
              when others then
                raise;
          end;
        else
          if nvl(vr_art_cfg.MHD_TOL,0) > 0 then
            v_tol := vr_art_cfg.MHD_TOL;
          else
            v_tol := pMHDToleranz;
          end if;
        end if;

        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': REF='||vr_auf.REF||', STATUS='||vr_auf.STATUS);

        select MANAGED_BY into v_manag from LAGER where REF=vr_auf.REF_LAGER;

        if (v_manag is not null) then
          oErrorCode := 2600;
          oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_auf.REF_LAGER), v_manag);
        else
          declare
            v_ok INTEGER;
          begin
            if (vr_auf.STATUS in ('ANG', 'FREI', 'BER', 'KOM', 'PLA')) then
              v_ok := 1;

              res := CHECK_TOUR (vr_auf, v_ok, oErrorCode, oErrorText);

              if (res = 0) and (oErrorCode = 0) then
                SetKommPlanLB (nvl (vr_auf.REF_SUB_MAND, vr_auf.REF_MAND), vr_auf.REF_LAGER, vr_auf.AUFTRAGSART);

                res := CHECK_BESTAND (vr_auf, v_res_id, v_tol, GetKommOpt (vr_auf), null, null, v_ok, oErrorCode, oErrorText);

                if (res = 0) and (v_ok = 1) then
                  --Wenn der Bestand jetzt ok war, kann der Backlog-Eintrag für den Auftrag zurückgesetzt werden
                  update AUFTRAG_BACKLOG set NEXT_CHECK=null where REF_AUF_KOPF=vr_auf.REF;
                end if;
              end if;
            end if;

            --Wenn in einem Auftrag der Bestand nicht ausreicht, dies vormerken
            if (v_ok = 0) then
              oOk := 0;
            end if;
          end;
        end if;

        exit when (res <> 0) or (oErrorCode <> 0);
      end loop;

      if (res = 0) and (oErrorCode = 0) Then
        res := RESET_RESERVIERUNG (v_res_id);
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oOk='||oOk);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function FREIGEBEN (prAuftrag       in  AUFTRAG%ROWTYPE,
                    pResID          in  LAGER_RES_BESTAND.RES_ID%TYPE,
                    prAufConfig     in AUFTRAG_ART_CONFIG%ROWTYPE,
                    oRefWA          in out WARENAUSGANG.REF%TYPE,
                    oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_rel      WA_RELATION%rowtype;
  vr_vers     AUFTRAG_VERSAND%rowtype;
  vr_komm_rel WARENEMPF_REL_KOMM%rowtype := null;

  v_auf_ref   AUFTRAG.REF%TYPE;
  v_ls_nr     AUFTRAG.LIEFERSCHEIN_NR%TYPE;
  v_ref_verl  WA_VERLADUNG.REF%type;

  v_count     INTEGER;

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('FREIGEBEN');
  BASE_TRACING.TraceOutput (2,'prAuftrag.REF  ='||prAuftrag.REF);
  BASE_TRACING.TraceOutput (2,'pResID         ='||pResID);
  BASE_TRACING.TraceOutput (2,'prAufConfig.REF='||prAufConfig.REF);

  --Nur wenn min. eine Auftrags-Position erfüllt werden kann, wird eine Kommissionierung angelegt
  select count (*) into v_count from LAGER_RES_BESTAND where RES_ID=pResID;

  if (v_count > 0) then
    select * into vr_vers from AUFTRAG_VERSAND where REF_AUF_KOPF=prAuftrag.REF;

    --Wenn noch keine Lieferschein-Nummer vergeben ist, wird hier dem Auftrag eine zugeordnet
    if (prAuftrag.LIEFERSCHEIN_NR is null) then
      res := PA_AUFTRAG_ABWICKLUNG.GET_AUFTRAG_LS_NUMMER (prAuftrag.REF, v_ls_nr, oErrorCode, oErrorText);

      if (res = 0) and (oErrorCode = 0) then
        if (v_ls_nr is not null) then
          --Die Lieferscheinnummer in alle noch zugehörigen Teilaufträge übernehmen
          update AUFTRAG set LIEFERSCHEIN_NR=v_ls_nr where REF_MASTER_AUF_KOPF=prAuftrag.REF_MASTER_AUF_KOPF;

          res := PA_SCHNITTSTELLE.Enqueueing (PA_SCHNITTSTELLE.IFC_QUEUE_LSNUMMER, prAuftrag.REF, oErrorCode, oErrorText);
        end if;
      end if;
    end if;

    if (res = 0) and (oErrorCode = 0) then
      if (prAuftrag.REF_PACKLISTE is null) then
        v_auf_ref := prAuftrag.REF;
      else
        select REF_AUF_KOPF into v_auf_ref from AUFTRAG_PACKLISTE where REF=prAuftrag.REF_PACKLISTE;
      end if;

      BASE_TRACING.TraceOutput(3, '#'||$$plsql_line||': prAuftrag.REF_WARENEMPF='||prAuftrag.REF_WARENEMPF||', vr_vers.REF_RELATION='||vr_vers.REF_RELATION);

      if (prAuftrag.REF_WARENEMPF is not null) then
        begin
          select * into vr_komm_rel from WARENEMPF_REL_KOMM where REF_WARENEMPF=prAuftrag.REF_WARENEMPF and REF_LAGER=prAuftrag.REF_LAGER;

          exception
            when no_data_found then
              vr_komm_rel := null;
            when others then
              raise;
        end;
      end if;

      BASE_TRACING.TraceOutput(3, '#'||$$plsql_line||': vr_komm_rel REF='||vr_komm_rel.REF||', REF_WA_RELATION='||vr_komm_rel.REF_WA_RELATION);

      if (vr_komm_rel.REF is not null) then
        --WA-Relation aus dem Warenempfänger übernehmen
        if (vr_komm_rel.REF_WA_RELATION is not null and vr_vers.REF_RELATION is null) then
          vr_vers.REF_RELATION := vr_komm_rel.REF_WA_RELATION;

          update AUFTRAG_VERSAND set REF_RELATION=vr_komm_rel.REF_WA_RELATION where REF_AUF_KOPF=prAuftrag.REF;
        end if;
      end if;

      --Prüfen ob es bereits einen offenen WA für den Auftrag gibt.
      begin
        select REF into oRefWA from WARENAUSGANG where STATUS='ANG' and REF in (select REF_WA from WA_REL_AUFTRAG where REF_AUFTRAG=v_auf_ref) and ROWNUM=1 for update;

        exception
          when no_data_found then
            oRefWA := null;

          when others then
            raise;
      end;

      --Wenn nicht, wird einer angelegt
      if (oRefWA is null) then
        res := PA_WA.CREATE_WARENAUSGANG (v_auf_ref, oRefWA, oErrorCode, oErrorText);
      end if;

      --Jetzt noch prüfen, ob der Auftrag auch wirklich mit dem WA verbunden ist
      select count (*) into v_count from WA_REL_AUFTRAG where REF_AUFTRAG=prAuftrag.REF and REF_WA=oRefWA;

      if (v_count = 0) Then
        --Wenn nicht diese Verbindung hinterlegen
        insert into WA_REL_AUFTRAG
            (REF_AUFTRAG, REF_WA)
          values
            (prAuftrag.REF, oRefWA);
      end if;
    end if;

    if (res = 0) then
      --Prüfen, ob die Verladung schon angelegt werden soll
      if (nvl (prAufConfig.OPT_VERLADUNG_BEI_FREIGABE, '0') = '1') then
        if (vr_vers.REF_RELATION is not null) then
          select * into vr_rel from WA_RELATION where REF=vr_vers.REF_RELATION;

          BASE_TRACING.TraceOutput(3, '#'||$$plsql_line||': vr_rel : REF='||vr_rel.REF||', OPT_VERLADUNG=' || vr_rel.OPT_VERLADUNG);

          --Wenn die Verladung schon bei der Freigabe angelegt werden soll
          if (nvl (vr_rel.OPT_VERLADUNG, '0') <> '0') then
            res := PA_VERLADUNG.GET_AUFTRAG_VERLADUNG (prAuftrag.REF, vr_rel.REF, v_ref_verl, oErrorCode, oErrorText);
          end if;
        end if;
      end if;
    end if;

    if (res = 0) then
      update AUFTRAG set COUNT_FREIGABE=nvl (COUNT_FREIGABE, 0) + 1 where REF=prAuftrag.REF;
      update AUFTRAG_TIMING set KOMM_PLANNING=sysdate where REF_AUF_KOPF=prAuftrag.REF;

      if (prAuftrag.STATUS = 'ANG') then
        res := PA_SCHNITTSTELLE.Enqueueing (PA_SCHNITTSTELLE.IFC_QUEUE_PLANKOM, prAuftrag.REF, oErrorCode, oErrorText);
      end if;

      if (res = 0) and (prAuftrag.STATUS in ('ANG', 'BER')) then
        res := PA_AUFTRAG_ABWICKLUNG.SET_AUFTRAG_STATUS (prAuftrag.REF, 'PLA', oErrorCode, oErrorText);
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oRefWA='||oRefWA);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode||', oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function FREIGEBEN (prAuftrag       in  AUFTRAG%ROWTYPE,
                    pResID          in  LAGER_RES_BESTAND.RES_ID%TYPE,
                    oRefWA          in out WARENAUSGANG.REF%TYPE,
                    oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_auf_cfg  AUFTRAG_ART_CONFIG%rowtype := null;

  res	        NUMBER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('FREIGEBEN');

  if (prAuftrag.REF_ART_CONFIG is not null) then
    select * into vr_auf_cfg from AUFTRAG_ART_CONFIG where REF=prAuftrag.REF_ART_CONFIG;
  end if;

  res := FREIGEBEN (prAuftrag, pResID, vr_auf_cfg, oRefWA, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--**************************************************************************************************************************
--*  Function Name     : RES_AUFPOS_OHNE_BESTAND
--*  Author            : Michaela Dornio
--**************************************************************************************************************************
--*  Description       : Hier werden die Positionen gesucht und gebucht, die separat gebucht werden müssen, da ohne Bestand
--*-------------------------------------------------------------------------------------------------------------------------
--*  Return Value      :
--**************************************************************************************************************************
function  RES_AUFPOS_OHNE_BESTAND (v_auf_kopf IN  AUFTRAG%ROWTYPE,
                                   v_auf_pos  IN  auftrag_pos%ROWTYPE,
                                   pID        in  LAGER_RES_BESTAND.RES_ID%TYPE,
                                   oErrorCode OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel                PLS_INTEGER;
  v_auf_mhd               AUFTRAG_POS.MHD_MIN%TYPE;
  v_auf_pos_ohne_bestand  AUFTRAG_POS%ROWTYPE;
  vr_artikel              ARTIKEL%ROWTYPE;
  vr_auf_pos_ae           ARTIKEL_EINHEIT%ROWTYPE;
  v_benoetigt             INTEGER;
  v_vpe_benoetigt         INTEGER;
  v_menge_used            LAGER_RES_BESTAND.MENGE_USED%TYPE;

  res	          NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.RES_AUFPOS_OHNE_BESTAND');
  BASE_TRACING.TraceOutput (2,'v_auf_kopf.ref         ='||v_auf_kopf.ref);
  BASE_TRACING.TraceOutput (2,'v_auf_pos.ref          ='||v_auf_pos.ref);
  BASE_TRACING.TraceOutput (2,'pID                    ='||pID);

  if(v_auf_pos.ref is null) then open c_auftrag_pos (v_auf_kopf.ref); end if;
    begin
      loop
        if(v_auf_pos.ref is null) then
          fetch c_auftrag_pos into v_auf_pos_ohne_bestand;
          exit when c_auftrag_pos%NOTFOUND;
        else
          v_auf_pos_ohne_bestand := v_auf_pos;
        end if;
          begin
            select sum (MENGE_USED) into v_menge_used FROM LAGER_RES_BESTAND WHERE RES_ID=pID AND REF_AUF_POS = v_auf_pos_ohne_bestand.REF;

            exception
              when no_data_found then
                v_menge_used := null;
              when others then
                raise;
          end;

          v_benoetigt     := NVL (v_auf_pos_ohne_bestand.MENGE_SOLL, 0) - NVL (v_auf_pos_ohne_bestand.MENGE_GEPLANT, 0) - NVL(v_menge_used,0);

          if ((nvl(v_auf_pos_ohne_bestand.menge_soll, 0) > 0)
              and (v_benoetigt > 0)
              and (v_auf_pos_ohne_bestand.CROSSDOCK_BESTELL_NR is null)) then
            select * into vr_artikel from ARTIKEL where REF=v_auf_pos_ohne_bestand.REF_AR;
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_artikel: REF='||vr_artikel.REF||', ARTIKEL_NR='||vr_artikel.ARTIKEL_NR);

            v_vpe_benoetigt := v_benoetigt;

            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': Pos_Nr='||v_auf_pos_ohne_bestand.POS_NR);

            begin
              if (v_auf_pos_ohne_bestand.REF_AR_EINHEIT is null) Then
                select * into vr_auf_pos_ae from ARTIKEL_EINHEIT where STATUS='AKT' and nvl (OPT_MASTER,'0')='1' and REF_AR=v_auf_pos_ohne_bestand.REF_AR and REF_EINHEIT=v_auf_pos_ohne_bestand.REF_EINHEIT;
              else
                select * into vr_auf_pos_ae from ARTIKEL_EINHEIT where REF=v_auf_pos_ohne_bestand.REF_AR_EINHEIT;
              end if;

              exception
                when no_data_found then
                  vr_auf_pos_ae := null;

                when others then
                  raise;
            end;

           if (nvl (vr_artikel.OPT_BESTAND, '1') = '0') then
              res := BUCHE_RES_BESTAND (v_auf_pos_ohne_bestand, vr_auf_pos_ae, vr_auf_pos_ae, null, null, null, v_benoetigt, v_vpe_benoetigt, pID, null, null, False, 2);
           else
             if (nvl (vr_artikel.OPT_MHD_PFLICHT, 'O') = 'O') then
               v_auf_mhd := null;
             elsif (v_auf_pos_ohne_bestand.MHD_MIN is null) then
               if (vr_artikel.RLZ_KOMM is not null) then
                 v_auf_mhd := v_auf_kopf.LIEFER_DATUM + vr_artikel.RLZ_KOMM;
               elsif (vr_artikel.RLZ_WA is not null) then
                 v_auf_mhd := v_auf_kopf.LIEFER_DATUM + vr_artikel.RLZ_WA;
               else
                 v_auf_mhd := null;
               end if;
             else
               v_auf_mhd := v_auf_pos_ohne_bestand.MHD_MIN;
             end if;

             BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': MHD='||TO_CHAR (v_auf_mhd, 'DD.MM.YYYY')||', AR_VARIANTE='||v_auf_pos_ohne_bestand.AR_VARIANTE);
             res := BUCHE_RES_BESTAND (v_auf_pos_ohne_bestand, vr_auf_pos_ae, vr_auf_pos_ae, null, v_auf_mhd, null, v_benoetigt, v_vpe_benoetigt, pID, null, null, False, 2);
           end if;
         end if;
      exit when (res <> 0) or (v_auf_pos.ref is not null);
   end loop;
   if(v_auf_pos.ref is null) then close c_auftrag_pos;end if;

  EXCEPTION WHEN OTHERS THEN
    if(v_auf_pos.ref is null) then close c_auftrag_pos; end if;
  raise;
end;

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;

end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function AUFTRAG_FREIGEBEN (pRefAuf         in  AUFTRAG.REF%TYPE,
                            pFlagFehlmengen in  VARCHAR2,
                            pMHDToleranz    in  INTEGER,
                            pKommBenRef     in  SYS_BEN.REF%TYPE,
                            pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                            oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                            oErrorCode      OUT INTEGER,
                            oErrorText      OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel      PLS_INTEGER;

  res	          NUMBER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.AUFTRAG_FREIGEBEN');

  res := AUFTRAG_FREIGEBEN (pRefAuf, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, null, oResID, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function AUFTRAG_FREIGEBEN (pRefAuf         in  AUFTRAG.REF%TYPE,
                            pFlagFehlmengen in  VARCHAR2,
                            pMHDToleranz    in  INTEGER,
                            pKommBenRef     in  SYS_BEN.REF%TYPE,
                            pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                            pMaxKommVPE     in  integer,
                            oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                            oErrorCode      OUT INTEGER,
                            oErrorText      OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel      PLS_INTEGER;

  vr_auf_kopf   AUFTRAG%ROWTYPE;
  vr_vers       AUFTRAG_VERSAND%ROWTYPE;

  vr_auf_cfg    AUFTRAG_ART_CONFIG%ROWTYPE := null;
  vr_auf_plan   AUFTRAG_ART_PLANUNG%ROWTYPE := null;

  vr_logi_param WARENEMPF_REL_KOMM%ROWTYPE;
  vr_tour       WA_REL_TOUR%ROWTYPE;

  v_ref_wa      WARENAUSGANG.REF%TYPE;
  v_manag       LAGER.MANAGED_BY%TYPE;

  v_ta_ref      TA_POS.REF%TYPE;

  v_tol         PLS_INTEGER;
  v_okflag      PLS_INTEGER;
  v_komm_count  PLS_INTEGER;
  v_lt_count    PLS_INTEGER;
  v_leer_komm   PLS_INTEGER;  --als Parameter definieren.

  res	          NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.AUFTRAG_FREIGEBEN');
  BASE_TRACING.TraceOutput (2,'pRefAuf         ='||pRefAuf);
  BASE_TRACING.TraceOutput (2,'pFlagFehlmengen ='||pFlagFehlmengen);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz    ='||pMHDToleranz);
  BASE_TRACING.TraceOutput (2,'pKommBenRef     ='||pKommBenRef);
  BASE_TRACING.TraceOutput (2,'pKommGrpRef     ='||pKommGrpRef);
  BASE_TRACING.TraceOutput (2,'pMaxKommVPE     ='||pMaxKommVPE);

  delete from TMP_VOR_RES;

  if (pFlagFehlmengen = '2') then
    v_leer_komm := 1;
  else
    v_leer_komm := 0;
  end if;

  select * into vr_auf_kopf from AUFTRAG where REF=pRefAuf;
  select * into vr_vers from AUFTRAG_VERSAND where REF_AUF_KOPF=pRefAuf;

  if (vr_auf_kopf.REF_ART_CONFIG is not null) then
    select * into vr_auf_cfg from AUFTRAG_ART_CONFIG where REF=vr_auf_kopf.REF_ART_CONFIG;
  end if;

  if (vr_auf_cfg.FREIGABE_FUNCTION is not null) then
    declare
      v_cur   INTEGER;
      fdbk    INTEGER;
    begin
      --Wenn für den Auftragstypen eine spezielle Freigabefunktion hinterlegt ist
      v_cur := dbms_sql.open_cursor;

      begin
        DBMS_SQL.PARSE (v_cur, 'BEGIN :res := '||vr_auf_cfg.FREIGABE_FUNCTION||' (:pRefAuf, :pFlagFehlmengen, :pKommBenRef, :pKommGrpRef, :pMaxKommVPE, :oResID, :oErrorCode, :oErrorText); END;', DBMS_SQL.NATIVE);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'res', 0);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'pRefAuf', pRefAuf);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'pFlagFehlmengen', pFlagFehlmengen, 1);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'pKommBenRef',pKommBenRef);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'pKommGrpRef',pKommGrpRef);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'pMaxKommVPE',pMaxKommVPE);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'oResID', oResID);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'oErrorCode', oErrorCode);
        DBMS_SQL.BIND_VARIABLE (v_cur, 'oErrorText', oErrorText, 256);

        fdbk := DBMS_SQL.EXECUTE (v_cur);

        DBMS_SQL.VARIABLE_VALUE (v_cur, 'res', res);

        if (res <> 0) then
          DBMS_SQL.VARIABLE_VALUE (v_cur, 'oResID', oResID);

          DBMS_SQL.VARIABLE_VALUE (v_cur, 'oErrorCode', oErrorCode);
          DBMS_SQL.VARIABLE_VALUE (v_cur, 'oErrorText', oErrorText);
        end if;

        exception
          when others then
           dbms_sql.close_cursor(v_cur);
           raise;
      end;

      dbms_sql.close_cursor(v_cur);
    end;
  else
    select MANAGED_BY into v_manag from LAGER where REF=vr_auf_kopf.REF_LAGER;

    if (v_manag is not null) then
      oErrorCode := 2601;
      oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_auf_kopf.REF_LAGER), v_manag);
    elsif (vr_auf_kopf.REF_BATCHLAUF is not null) then
      declare
        v_nr varchar2 (32);
      begin
        select BATCH_NR into v_nr from AUFTRAG_BATCHLAUF where REF=vr_auf_kopf.REF_BATCHLAUF;

        oErrorCode := 2605;
        oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, vr_auf_kopf.AUFTRAG_NR, v_nr);
      end;
    elsif ((nvl (vr_auf_cfg.OPT_WA_REL_ZUORDNUNG, '0') = '1') and vr_vers.REF_RELATION is null) then
      oErrorCode := 2606;
      oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, vr_auf_kopf.AUFTRAG_NR);
    else
      v_ref_wa := null;

      if (vr_auf_kopf.STATUS in ('ANG', 'FREI', 'BER', 'KOM', 'PLA')) then
        res := CHECK_TOUR (vr_auf_kopf, v_okflag, oErrorCode, oErrorText);

        if (res = 0) and (oErrorCode = 0) then
       if (pMHDToleranz is null) and nvl(vr_auf_cfg.MHD_TOL,0) = 0  then
              if (vr_auf_kopf.REF_WARENEMPF is not null) Then
                select MHD_TOL into v_tol from WARENEMPF where REF=vr_auf_kopf.REF_WARENEMPF;
              else
                v_tol := 0;
              end if;
            else
              if nvl(vr_auf_cfg.MHD_TOL,0) > 0 then
                v_tol := vr_auf_cfg.MHD_TOL;
              else
                v_tol := pMHDToleranz;
              end if;
            end if;

            begin
              select * into vr_logi_param from (select * from WARENEMPF_REL_KOMM where REF_WARENEMPF=vr_auf_kopf.REF_WARENEMPF and ((REF_LAGER is not null and REF_LAGER=vr_auf_kopf.REF_LAGER) or (REF_LAGER is null and REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=vr_auf_kopf.REF_LAGER))) order by REF_LAGER nulls last) where ROWNUM=1;

              exception
                when no_data_found then
                  vr_logi_param := null;
                when others then
                  raise;
              end;

            if (vr_vers.REF_TOUR is null) then
              vr_tour := null;
            else
              begin
                select * into vr_tour from WA_REL_TOUR where REF=vr_vers.REF_TOUR;

                exception
                  when no_data_found then
                    vr_tour := null;
                  when others then
                    raise;
              end;
            end if;

            delete from TMP_PLAN_RES_ID;

            select SEQ_ID.NEXTVAL into oResID from dual;
            insert into TMP_PLAN_RES_ID (RES_ID) values (oResID);

            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': oResID='||oResID);

            if (vr_auf_kopf.REF_ART_PLANUNG is not null) then
              select * into vr_auf_plan from AUFTRAG_ART_PLANUNG where REF=vr_auf_kopf.REF_ART_PLANUNG;
            else
              begin
                select * into vr_auf_plan from (select * from AUFTRAG_ART_PLANUNG where KOMM_ART is null and REF_MAND=vr_auf_kopf.REF_MAND and (REF_SUB_MAND is null or (nvl (REF_SUB_MAND,-1)=nvl (vr_auf_kopf.REF_SUB_MAND, -1))) and REF_LAGER=vr_auf_kopf.REF_LAGER and (AUFTRAG_ART is null or AUFTRAG_ART=vr_auf_kopf.AUFTRAGSART) order by AUFTRAG_ART nulls last) where ROWNUM=1;

                exception
                  when no_data_found then
                    vr_auf_plan := null;
                  when others then
                    raise;
              end;
            end if;

            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_plan: REF='||vr_auf_plan.REF||', OPT_KOMM_ART='||vr_auf_plan.OPT_KOMM_ART);

            SetKommPlanLB (nvl (vr_auf_kopf.REF_SUB_MAND, vr_auf_kopf.REF_MAND), vr_auf_kopf.REF_LAGER, vr_auf_kopf.AUFTRAGSART);

            res := CHECK_BESTAND (vr_auf_kopf, oResID, v_tol, GetKommOpt (vr_auf_kopf), vr_auf_plan, null, v_okflag, oErrorCode, oErrorText);

            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_okflag='||v_okflag);

            if (res = 0) and (oErrorCode = 0) then
              if (v_okflag <> 1) and (pFlagFehlmengen = '0') then
                res := RESET_RESERVIERUNG (oResID);
              else
                BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_leer_komm='||v_leer_komm);

                --alle Positionen des Auftrages durchgehen, bei denen kein Bestand vorhanden ist und diesen freigeben.
                --evtl. neuen Parameter festlegen dafür
                --Soll-MHD/Charge muss ermittelt werden, Plan-MHD/Charge ist leer.
                if (v_okflag <> 1) and (v_leer_komm = 1) then
                  res:= RES_AUFPOS_OHNE_BESTAND (vr_auf_kopf, null, oResID, oErrorCode, oErrorText);
                end if;

                if (res = 0) and (oErrorCode = 0) then
                  --Nur wenn min. eine Auftrags-Position, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
                  select count (*) into v_komm_count from LAGER_RES_BESTAND r where r.RES_ID=oResID and REF_AUF_POS_LT is null and (select CROSSDOCK_BESTELL_NR from AUFTRAG_POS where REF=r.REF_AUF_POS) is null;
                  select count (*) into v_lt_count from LAGER_RES_BESTAND r where r.RES_ID=oResID and REF_AUF_POS_LT is not null;

                  if (v_komm_count = 0) and (v_lt_count = 0) then
                    res := RESET_RESERVIERUNG (oResID);
                  else
                    --Die Vorreservierungen zurücksetzen
                    delete from LAGER_VOR_RES_BESTAND where REF_AUF_POS in (select REF from AUFTRAG_POS where REF_AUF_KOPF=pRefAuf);
                    update AUFTRAG_POS set MENGE_VOR_RES=null where REF in (select REF from AUFTRAG_POS where REF_AUF_KOPF=pRefAuf);

                    res := FREIGEBEN (vr_auf_kopf, oResID, v_ref_wa, oErrorCode, oErrorText);

                    if (res = 0) and (oErrorCode = 0) Then
                      if (v_lt_count > 0) then
                        for cr_res in (select * from LAGER_RES_BESTAND where RES_ID=oResID and REF_AUF_POS_LT is not null) loop
                          res := PA_TA.CREATE_NVE_UMLAGERUNG (vr_auf_kopf.REF_LAGER, 'AUSLAGERN', 'NVE', null, null, null, cr_res.REF_NVE, v_ta_ref, oErrorCode, oErrorText);

                          if (res = 0) then
                            update TA set RES_ID=oResID, REF_AUF_POS_LT=cr_res.REF_AUF_POS_LT, REF_AUFTRAG=vr_auf_kopf.REF where REF=v_ta_ref;
                            --update LAGER_NVE set REF_AUF_KOPF=vr_auf_kopf.REF where REF=cr_res.REF_NVE;
                          end if;

                          exit when (res <> 0);
                        end loop;
                      end if;

                      if (res = 0) then
                        if (v_komm_count > 0) then
                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_auf_plan.OPT_KOMM_ART='||vr_auf_plan.OPT_KOMM_ART);

                          if (vr_auf_plan.OPT_KOMM_ART = 'L') then
                            --Bereitstellung: Pro Auftragpos eine Kommpos., keine Bestandsplanung, keine Verpackung usw.
                            res := PA_KOMM_PLANUNG.KOMM_SIMPLE_PLANEN (oResID, 'LOAD', null, pKommBenRef, pKommGrpRef, oErrorCode, oErrorText);
                          elsif (vr_auf_plan.OPT_KOMM_ART in ('W', 'O')) then
                            --Pick u Pack: alle Picks werden direkt verpackt und mit Versandlabels versehen
                            res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, null, oResID, 'PP', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, v_leer_komm, null, oErrorCode, oErrorText);
                          else
                            --Alle reservierten Bestande werden zu Kommissionierpositionen, mit oder ohne Packprozess im WA
                            res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, null, oResID, 'VE', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, v_leer_komm, pMaxKommVPE, oErrorCode, oErrorText);
                          end if;
                        elsif (v_lt_count > 0) then
                          update AUFTRAG set STATUS='PLA' where REF=vr_auf_kopf.REF;
                        end if;
                      end if;

                      /*Wird nicht mehr benötigt
                      if (res = 0) and (oErrorCode = 0) then
                        res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_SPED_DFUE (vr_auf_kopf.REF, oErrorCode, oErrorText);
                      end if;
                      */
                    end if;
                  end if;
                end if;
              end if;

              if (res = 0) and (oErrorCode = 0) then
                res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_STATUS (vr_auf_kopf.REF, oErrorCode, oErrorText);
              end if;
            end if;
          end if;
        end if;
      end if;
    end if;

  BASE_TRACING.TraceOutput (2, 'oResID='||oResID);
  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function PACKLISTE_FREIGEBEN (pRefPack        in  AUFTRAG_PACKLISTE.REF%TYPE,
                              pFlagFehlmengen in  VARCHAR2,
                              pMHDToleranz    in  INTEGER,
                              pKommBenRef     in  SYS_BEN.REF%TYPE,
                              pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                              oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                              oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_pack     AUFTRAG_PACKLISTE%ROWTYPE;
  vr_tour     WA_REL_TOUR%ROWTYPE;
  vr_auf_cfg  AUFTRAG_ART_CONFIG%ROWTYPE;
  vr_auf      AUFTRAG%ROWTYPE;

  vr_logi_param WARENEMPF_REL_KOMM%ROWTYPE;

  v_ref_wa    WARENAUSGANG.REF%TYPE;
  v_manag     LAGER.MANAGED_BY%TYPE;

  v_tol       INTEGER;
  v_okflag    INTEGER;
  v_leer_komm INTEGER;
  v_count     integer;
  pFlagKommBestand  integer;  --als Parameter definieren.

  res	        NUMBER;
begin
  res := 0;

  pFlagKommBestand := 1;  --als Parameter definieren.
  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.PACKLISTE_FREIGEBEN');
  BASE_TRACING.TraceOutput (2,'pRefPack       ='||pRefPack);
  BASE_TRACING.TraceOutput (2,'pFlagFehlmengen='||pFlagFehlmengen);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz   ='||pMHDToleranz);
  BASE_TRACING.TraceOutput (2,'pKommBenRef    ='||pKommBenRef);
  BASE_TRACING.TraceOutput (2,'pKommGrpRef    ='||pKommGrpRef);

  if (pFlagFehlmengen = '2') then
    v_leer_komm := 1;
  else
    v_leer_komm := 0;
  end if;

  delete from TMP_VOR_RES;

  select * into vr_pack from AUFTRAG_PACKLISTE where REF=pRefPack;

  if (vr_pack.REF_LAGER is null) then
    v_manag := null;
  else
    select MANAGED_BY into v_manag from LAGER where REF=vr_pack.REF_LAGER;
  end if;

  if (v_manag is not null) then
    oErrorCode := 2602;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_pack.REF_LAGER), v_manag);
  else
    if (vr_pack.STATUS in ('ANG', 'BER', 'KOM', 'PLA')) then
      --Referenz auf die Fehlerliste löschen
      update AUFTRAG_PACKLISTE set FEHLER_STATUS=null where REF=pRefPack;
      update AUFTRAG set FEHLER_STATUS=null where REF_PACKLISTE=pRefPack;

      delete from FEHLER_LISTE where REF_ID=pRefPack;
      delete from FEHLER_LISTE where REF_ID in (select REF from AUFTRAG where REF_PACKLISTE=pRefPack);

      delete from TMP_PLAN_RES_ID;

      select SEQ_ID.NEXTVAL into oResID from dual;
      insert into TMP_PLAN_RES_ID (RES_ID) values (oResID);

      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': oResID='||oResID);

      if (pMHDToleranz is null) then
        begin
          select MHD_TOL into v_tol from WARENEMPF where REF=(select REF_WARENEMPF from AUFTRAG where REF=vr_pack.REF_AUF_KOPF);

          exception
            when no_data_found then
              v_tol := 0;
            when others then
              raise;
        end;
        if vr_pack.ref_auf_kopf is not null then
           select * into vr_auf from AUFTRAG where REF=vr_pack.REF_AUF_KOPF;
           select * into vr_auf_cfg from AUFTRAG_ART_CONFIG where REF=vr_auf.REF_ART_CONFIG;
           if nvl(vr_auf_cfg.MHD_TOL,0) > 0  then
             v_tol := vr_auf_cfg.MHD_TOL;
           end if;
        end if;
      else
        v_tol := pMHDToleranz;
      end if;

      begin
        select * into vr_logi_param from WARENEMPF_REL_KOMM where REF_WARENEMPF=vr_pack.REF_WARENEMPF and (REF_LAGER=vr_pack.REF_LAGER or REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=vr_pack.REF_LAGER));

        exception
          when no_data_found then
            vr_logi_param := null;
          when others then
            raise;
      end;

      if (vr_pack.REF_TOUR is null) then
        vr_tour := null;
      else
        begin
          select * into vr_tour from WA_REL_TOUR where REF=vr_pack.REF_TOUR;

          exception
            when no_data_found then
              vr_tour := null;
            when others then
              raise;
        end;
      end if;

      v_okflag := 1;

      for vr_auf in (select * from AUFTRAG where REF_PACKLISTE=pRefPack) loop
        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': REF='||vr_auf.REF||', STATUS='||vr_auf.STATUS);

        select MANAGED_BY into v_manag from LAGER where REF=vr_auf.REF_LAGER;

        if (v_manag is not null) then
          oErrorCode := 2602;
          oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_auf.REF_LAGER), v_manag);
        elsif (vr_auf.REF_BATCHLAUF is not null) then
          declare
            v_nr varchar2 (32);
          begin
            select BATCH_NR into v_nr from AUFTRAG_BATCHLAUF where REF=vr_auf.REF_BATCHLAUF;

            oErrorCode := 2605;
            oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, vr_auf.AUFTRAG_NR, v_nr);
          end;
        else
          declare
            v_ok INTEGER;
          begin
            if (vr_auf.STATUS in ('ANG', 'FREI', 'BER', 'KOM', 'PLA')) then
              v_ok := 1;

              res := CHECK_TOUR (vr_auf, v_ok, oErrorCode, oErrorText);

              if (res = 0) and (oErrorCode = 0) then
                SetKommPlanLB (nvl (vr_auf.REF_SUB_MAND, vr_auf.REF_MAND), vr_auf.REF_LAGER, vr_auf.AUFTRAGSART);

                res := CHECK_BESTAND (vr_auf, oResID, v_tol, GetKommOpt (vr_auf), null, null, v_ok, oErrorCode, oErrorText);
              end if;
            end if;

            --Wenn in einem Auftrag der Bestand nicht ausreicht, dies vormerken
            if (v_ok = 0) then
              v_okflag := 0;
            end if;
          end;
        end if;

        exit when (res <> 0) or (oErrorCode <> 0);
      end loop;

      if (res = 0) and (oErrorCode = 0) Then
        if (res = 0) and (oErrorCode = 0) then
          for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
            res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_STATUS (cr_auf.REF, oErrorCode, oErrorText);

            exit when (res <> 0) or (oErrorCode <> 0);
          end loop;
        end if;

        if (v_okflag <> 1) and (pFlagFehlmengen = '0') then
          res := RESET_RESERVIERUNG (oResID);
        else
          --alle Positionen des Auftrages durchgehen, bei denen kein Bestand vorhanden ist und diesen freigeben.
          --evtl. neuen Parameter festlegen dafür
          --Soll-MHD/Charge muss ermittelt werden, Plan-MHD/Charge ist leer.
          if(v_okflag <> 1) and (v_leer_komm = 1) then
            for vr_auf in (select * from AUFTRAG where REF_PACKLISTE=pRefPack) loop
              res:=  RES_AUFPOS_OHNE_BESTAND(vr_auf, null, oResID, oErrorCode, oErrorText);
              exit when (res <> 0) or (oErrorCode <> 0);
            end loop;
          end if;

          if (res = 0) and (oErrorCode = 0) Then
          --Nur wenn min. eine Auftrags-Position, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
            select count (*) into v_count from LAGER_RES_BESTAND r where r.RES_ID=oResID and (select crossdock_bestell_nr from AUFTRAG_POS where REF=r.REF_AUF_POS) is null;

            if (v_count = 0) then
              res := RESET_RESERVIERUNG (oResID);
            else
              for vr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
                --Die Vorreservierungen zurücksetzen
                delete from LAGER_VOR_RES_BESTAND where REF_AUF_POS in (select REF from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF);
                update AUFTRAG_POS set MENGE_VOR_RES=null where REF in (select REF from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF);

                res := FREIGEBEN (vr_auf, oResID, v_ref_wa, oErrorCode, oErrorText);

                exit when (res <> 0) or (oErrorCode <> 0);
              end loop;

              if (res = 0) and (oErrorCode = 0) Then
                --Alle reservierten Bestande werden zu Kommissionierpositionen
                res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, null, oResID, 'VE', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, pFlagKommBestand, null, oErrorCode, oErrorText);
              end if;

              if (res = 0) and (oErrorCode = 0) then
                update AUFTRAG_PACKLISTE set COUNT_FREIGABE=nvl (COUNT_FREIGABE, 0) + 1 where REF=pRefPack;

                /*Wird nicht mehr benötigt
                for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
                  res := Pa_Schnittstelle.MELDE_AUFTRAG_SPED_DFUE (cr_auf.REF, oErrorCode, oErrorText);

                  exit when (res <> 0) or (oErrorCode <> 0);
                end loop;
                */
              end if;
            end if;
          end if;
        end if;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oResID='||oResID);
  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function KOMMGRUPPE_FREIGEBEN (pRefKommGrp     in  ARTIKEL_KOMM_GRUPPE.REF%TYPE,
                               pKommDatum      in  DATE,
                               pFlagFehlmengen in  VARCHAR2,
                               pMHDToleranz    in  INTEGER,
                               pKommBenRef     in  SYS_BEN.REF%TYPE,
                               pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                               oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                               oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_auf         AUFTRAG%ROWTYPE;
  vr_ar_komm_grp ARTIKEL_KOMM_GRUPPE%ROWTYPE;

  vr_logi_param WARENEMPF_REL_KOMM%ROWTYPE;

  v_ref_wa    WARENAUSGANG.REF%TYPE;
  v_stat      FEHLER_LISTE.FEHLER_STATUS%TYPE;

  v_okflag    INTEGER;
  v_count     integer;

  v_tab       PLS_INTEGER;
  pFlagKommBestand  integer;  --als Parameter definieren.

  res	        NUMBER;
begin
  res := 0;

  pFlagKommBestand := 1;  --als Parameter definieren.
  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.KOMMGRUPPE_FREIGEBEN');
  BASE_TRACING.TraceOutput (2,'pRefKommGrp    ='||pRefKommGrp);
  BASE_TRACING.TraceOutput (2,'pKommDatum     ='||pKommDatum);
  BASE_TRACING.TraceOutput (2,'pFlagFehlmengen='||pFlagFehlmengen);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz   ='||pMHDToleranz);
  BASE_TRACING.TraceOutput (2,'pKommBenRef    ='||pKommBenRef);

  delete from TMP_VOR_RES;

  select * into vr_ar_komm_grp from ARTIKEL_KOMM_GRUPPE where REF=pRefKommGrp;

  delete from TMP_PLAN_RES_ID;

  select SEQ_ID.NEXTVAL into oResID from dual;
  insert into TMP_PLAN_RES_ID (RES_ID) values (oResID);

  BASE_TRACING.TraceOutput (3,'oResID='||oResID);

  FehlerTab.delete;

  for cr_auf in (select * from AUFTRAG where STATUS not in ('FIN', 'ABG', 'TRA') and REF_LAGER=vr_ar_komm_grp.REF_LAGER and REF_MAND=vr_ar_komm_grp.REF_MAND and KOMM_DATUM=trunc (pKommDatum) and REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF_AR in (select REF_AR from ARTIKEL_REL_KOMM_GRUPPE where REF_KOMM_GRP=pRefKommGrp) and nvl (MENGE_SOLL, 0) > nvl (MENGE_GESAMT, 0))) loop
    BASE_TRACING.TraceOutput (3,'cr_auf.REF='||cr_auf.REF);

    --Referenz auf die Fehlerliste löschen
    update AUFTRAG set FEHLER_STATUS=null where REF=cr_auf.REF;
    delete from FEHLER_LISTE where REF_ID=cr_auf.REF;

    for cr_auf_pos in (select * from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF and REF_AR in (select REF_AR from ARTIKEL_REL_KOMM_GRUPPE where REF_KOMM_GRP=pRefKommGrp) and nvl (MENGE_SOLL, 0) > nvl (MENGE_GESAMT, 0)) loop
      BASE_TRACING.TraceOutput (3,'cr_auf_pos.REF='||cr_auf_pos.REF||', cr_auf_pos.POS_NR='||cr_auf_pos.POS_NR);

      res := CHECK_AUFTRAG_POS_BESTAND (cr_auf, null, cr_auf_pos, oResID, pMHDToleranz, GetKommOpt (cr_auf), null, v_stat, oErrorCode, oErrorText);

      exit when (res <> 0) or (oErrorCode <> 0);
    end loop;

    exit when (res <> 0) or (oErrorCode <> 0);
  end loop;

  if (res = 0) and (oErrorCode = 0) then
    v_tab := FehlerTab.FIRST;

    if (v_tab is null) then
      v_okflag := 1;
    else
      v_okflag := 0;

      res := CreateFehlerliste ('BESTAND', '');
    end if;
  end if;

  if (res = 0) and (oErrorCode = 0) then
    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_okflag='||v_okflag||', pFlagFehlmengen='||pFlagFehlmengen);

    if (v_okflag <> 1) and (pFlagFehlmengen = '0') then
      res := RESET_RESERVIERUNG (oResID);
    else
       if(v_okflag <> 1) and (pFlagKommBestand = '1') then
         for cr_auf2 in (select * from AUFTRAG where STATUS not in ('FIN', 'ABG', 'TRA') and REF_LAGER=vr_ar_komm_grp.REF_LAGER and REF_MAND=vr_ar_komm_grp.REF_MAND and KOMM_DATUM=trunc (pKommDatum) and REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF_AR in (select REF_AR from ARTIKEL_REL_KOMM_GRUPPE where REF_KOMM_GRP=pRefKommGrp) and (nvl (MENGE_SOLL, 0) > nvl (MENGE_GEPLANT, 0)))) loop
            for cr_auf_pos2 in (select * from AUFTRAG_POS where REF_AUF_KOPF=cr_auf2.REF and REF_AR in (select REF_AR from ARTIKEL_REL_KOMM_GRUPPE where REF_KOMM_GRP=pRefKommGrp) and (nvl (MENGE_SOLL, 0) > nvl (MENGE_GEPLANT, 0))) loop
               res:=  RES_AUFPOS_OHNE_BESTAND(cr_auf2, cr_auf_pos2, oResID, oErrorCode, oErrorText);
                exit when (res <> 0) or (oErrorCode <> 0);
            end loop;
            exit when (res <> 0) or (oErrorCode <> 0);
         end loop;

      end if;
      --Nur wenn min. eine Auftrags-Position, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
      select count (*) into v_count from LAGER_RES_BESTAND r where r.RES_ID=oResID;

      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_count='||v_count);

      if (v_count = 0) then
        res := RESET_RESERVIERUNG (oResID);
      else


        for vr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where res_id=oResID))) loop
          --Nur wenn min. eine Auftrags-Position, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
          select count (*) into v_count from LAGER_RES_BESTAND r where r.RES_ID=oResID and (select CROSSDOCK_BESTELL_NR from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF and REF=r.REF_AUF_POS) is null;

          if (v_count > 0) then
            --Die Vorreservierungen zurücksetzen
            delete from LAGER_VOR_RES_BESTAND where REF_AUF_POS in (select REF from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF);
            update AUFTRAG_POS set MENGE_VOR_RES=null where REF in (select REF from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF);

            res := FREIGEBEN (vr_auf, oResID, v_ref_wa, oErrorCode, oErrorText);

            /*Wird nicht mehr benötigt
            if (res = 0) and (oErrorCode = 0) then
              res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_SPED_DFUE (vr_auf.REF, oErrorCode, oErrorText);
            end if;
            */
          end if;

          exit when (res <> 0) or (oErrorCode <> 0);
        end loop;

        if (res = 0) and (oErrorCode = 0) Then
          --Alle reservierten Bestande werden zu Kommissionierpositionen
          res := PA_KOMM_PLANUNG.KOMM_PLANEN (pRefKommGrp, null, oResID, 'AR', pKommDatum, pKommBenRef, pKommGrpRef, vr_logi_param, null, pFlagKommBestand, null, oErrorCode, oErrorText);
        end if;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;


--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_FREIGEBEN (pRefBatch       in  AUFTRAG_BATCHLAUF.REF%TYPE,
                              pFlagFehlmengen in  VARCHAR2,
                              pMHDToleranz    in  INTEGER,
                              pKommBenRef     in  SYS_BEN.REF%TYPE,
                              pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                              oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                              oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is
  aktlevel    PLS_INTEGER;

  res	        NUMBER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLIST_FREIGEBEN');

  res := BATCHLAUF_FREIGEBEN (pRefBatch, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, null, oResID, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_FREIGEBEN (pRefBatch       in  AUFTRAG_BATCHLAUF.REF%TYPE,
                              pFlagFehlmengen in  VARCHAR2,
                              pMHDToleranz    in  INTEGER,
                              pKommBenRef     in  SYS_BEN.REF%TYPE,
                              pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                              pMaxKommVPE     in  integer,
                              oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                              oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_batch    AUFTRAG_BATCHLAUF%ROWTYPE;
  vr_tour     WA_REL_TOUR%ROWTYPE;
  vr_bat_plan AUFTRAG_ART_PLANUNG%rowtype;
  vr_bat_cfg  BATCHLAUF_CONFIG%rowtype;
  vr_art_cfg  AUFTRAG_ART_CONFIG%ROWTYPE;

  vr_logi_param WARENEMPF_REL_KOMM%ROWTYPE;

  v_ref_wa    WARENAUSGANG.REF%TYPE;
  v_manag     LAGER.MANAGED_BY%TYPE;

  v_tol       INTEGER;
  v_okflag    INTEGER;
  v_leer_komm INTEGER;
  v_count     integer;
  pFlagKommBestand  integer;  --als Parameter definieren.

  res	        NUMBER;
begin
  res := 0;

  pFlagKommBestand := 1;  --als Parameter definieren.
  oErrorCode := 0;
  oErrorText := NULL;

  lTraceLevel := BASE_TRACING.GETTRACELEVEL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLIST_FREIGEBEN');
  BASE_TRACING.TraceOutput (2,'pRefBatch      ='||pRefBatch);
  BASE_TRACING.TraceOutput (2,'pFlagFehlmengen='||pFlagFehlmengen);
  BASE_TRACING.TraceOutput (2,'pMHDToleranz   ='||pMHDToleranz);
  BASE_TRACING.TraceOutput (2,'pKommBenRef    ='||pKommBenRef);
  BASE_TRACING.TraceOutput (2,'pKommGrpRef    ='||pKommGrpRef);
  BASE_TRACING.TraceOutput (2,'pMaxKommVPE    ='||pMaxKommVPE);

  delete from TMP_VOR_RES;

  if (pFlagFehlmengen = '2') then
    v_leer_komm := 1;
  else
    v_leer_komm := 0;
  end if;

  select * into vr_batch from AUFTRAG_BATCHLAUF where REF=pRefBatch;
  select * into vr_bat_cfg from BATCHLAUF_CONFIG where REF=vr_batch.REF_BATCHLAUF_CONFIG;

  if (vr_batch.REF_LAGER is null) then
    v_manag := null;
  else
    select MANAGED_BY into v_manag from LAGER where REF=vr_batch.REF_LAGER;
  end if;

  if (v_manag is not null) then
    oErrorCode := 4;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (vr_batch.REF_LAGER), v_manag);
  elsif (vr_batch.STATUS = 'ABG') then
    oErrorCode := 3070;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null);
  elsif (vr_batch.STATUS = 'PLA') then
    oErrorCode := 3071;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null);
  elsif (vr_batch.STATUS = 'KOM') then
    oErrorCode := 3072;
    oErrorText := PA_ERROR.GetErrorText (oErrorCode, null);
  else
    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bat_cfg: BATCH_AUFTRAG_ART='||vr_bat_cfg.BATCH_AUFTRAG_ART||', OPT_SINGLE_POS_ORDER='||vr_bat_cfg.OPT_SINGLE_POS_ORDER||', OPT_SPERRGUT='||vr_bat_cfg.OPT_SPERRGUT||', OPT_SPERRGUT_ONLY='||vr_bat_cfg.OPT_SPERRGUT_ONLY);

    delete TMP_AUF;

    insert into TMP_AUF (REF_AUF_KOPF) select REF from AUFTRAG where REF_BATCHLAUF=pRefBatch;

    --Referenz auf die Fehlerliste löschen
    update AUFTRAG_BATCHLAUF set FEHLER_STATUS=null where REF=pRefBatch;
    update AUFTRAG set FEHLER_STATUS=null where REF in (select REF_AUF_KOPF from TMP_AUF);

    delete from FEHLER_LISTE where REF_ID=pRefBatch;
    delete from FEHLER_LISTE where REF_ID in (select REF_AUF_KOPF from TMP_AUF);

    delete from TMP_PLAN_RES_ID;

    select SEQ_ID.NEXTVAL into oResID from dual;
    insert into TMP_PLAN_RES_ID (RES_ID) values (oResID);

    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': oResID='||oResID);

    vr_tour := null;

    v_okflag := 1;

    SetKommPlanLB (vr_batch.REF_MAND, vr_batch.REF_LAGER, 'BAT-'||vr_batch.AUFTRAG_ART, vr_batch.REF_BATCHLAUF_CONFIG);

    --AUFTRAG_ART_PLANUNG für die Auftragsart ermitteln
    res := PA_AUFTRAG_VERWALTUNG.GET_AUFTRAG_PLANUNG (vr_batch.REF_MAND, null, vr_batch.REF_LAGER, vr_batch.AUFTRAG_ART, 'BAT', vr_bat_plan, oErrorCode, oErrorText);

    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bat_plan: REF='||vr_bat_plan.REF||', OPT_KOMM_ART='||vr_bat_plan.OPT_KOMM_ART);

    for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from TMP_AUF)) loop
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': REF='||cr_auf.REF||', STATUS='||cr_auf.STATUS);
      select * into vr_art_cfg from AUFTRAG_ART_CONFIG where ref = cr_auf.ref_art_config;
      if (pMHDToleranz is null) AND nvl(vr_art_cfg.MHD_TOL,0) = 0  then
        v_tol := 0;

        begin
          select MHD_TOL into v_tol from WARENEMPF where REF=(select REF_WARENEMPF from AUFTRAG where REF=cr_auf.REF);

          exception
            when no_data_found then
              v_tol := 0;
            when others then
              raise;
        end;
      else
        if nvl(vr_art_cfg.MHD_TOL,0) > 0 then
          v_tol := vr_art_cfg.MHD_TOL;
        else
          v_tol := pMHDToleranz;
        end if;
      end if;

      select MANAGED_BY into v_manag from LAGER where REF=cr_auf.REF_LAGER;

      if (v_manag is not null) then
        oErrorCode := 2600;
        oErrorText := PA_ERROR.GetErrorText (oErrorCode, null, PA_LAGER.GET_LAGER_NAME (cr_auf.REF_LAGER), v_manag);
      else
        declare
          v_ok INTEGER;
        begin
          if (cr_auf.STATUS in ('ANG', 'FREI', 'BER', 'KOM', 'PLA')) then
            v_ok := 1;

            res := CHECK_TOUR (cr_auf, v_ok, oErrorCode, oErrorText);

            if (res = 0) and (oErrorCode = 0) then
              res := CHECK_BESTAND (cr_auf, oResID, v_tol, GetKommOpt (cr_auf), vr_bat_plan, vr_bat_cfg, v_ok, oErrorCode, oErrorText);
            end if;
          end if;

          --Wenn in einem Auftrag der Bestand nicht ausreicht, dies vormerken
          if (v_ok = 0) then
            v_okflag := 0;
          end if;
        end;
      end if;

      exit when (res <> 0) or (oErrorCode <> 0);
    end loop;

    if (res = 0) and (oErrorCode = 0) Then
      if (res = 0) and (oErrorCode = 0) then
        for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
          res := PA_SCHNITTSTELLE.Enqueueing (PA_SCHNITTSTELLE.IFC_QUEUE_AUFTRAG_STATUS, cr_auf.REF, oErrorCode, oErrorText);

          exit when (res <> 0) or (oErrorCode <> 0);
        end loop;
      end if;

      if (v_okflag <> 1) and (pFlagFehlmengen = '0') then
        res := RESET_RESERVIERUNG (oResID);
      else
        --alle Positionen des Auftrages durchgehen, bei denen kein Bestand vorhanden ist und diesen freigeben.
        --evtl. neuen Parameter festlegen dafür
        --Soll-MHD/Charge muss ermittelt werden, Plan-MHD/Charge ist leer.
        if(v_okflag <> 1) and (v_leer_komm = 1) then
          for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from TMP_AUF)) loop
            res:=  RES_AUFPOS_OHNE_BESTAND(cr_auf, null, oResID, oErrorCode, oErrorText);
            exit when (res <> 0) or (oErrorCode <> 0);
          end loop;
        end if;

        if (res = 0) and (oErrorCode = 0) Then
          --Nur wenn min. eine Auftrags-Position, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
          select count (*) into v_count from LAGER_RES_BESTAND r where r.RES_ID=oResID and (select crossdock_bestell_nr from AUFTRAG_POS where REF=r.REF_AUF_POS) is null;

          if (v_count = 0) then
            res := RESET_RESERVIERUNG (oResID);
          else
            for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
              --Die Vorreservierungen zurücksetzen
              delete from LAGER_VOR_RES_BESTAND where REF_AUF_POS in (select REF from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF);
              update AUFTRAG_POS set MENGE_VOR_RES=null where REF in (select REF from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF);

              res := FREIGEBEN (cr_auf, oResID, v_ref_wa, oErrorCode, oErrorText);

              exit when (res <> 0) or (oErrorCode <> 0);
            end loop;

            if (res = 0) and (oErrorCode = 0) Then
              update AUFTRAG_BATCHLAUF set COUNT_FREIGABE=nvl (COUNT_FREIGABE, 0) + 1 where REF=pRefBatch;

              BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_batch.AUFTRAG_ART='||vr_batch.AUFTRAG_ART);

              if (vr_batch.AUFTRAG_ART in ('AUF', 'SAMMEL', 'CROSS')) then
                for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
                  declare
                    v_vpe       PLS_INTEGER;
                    v_gw        PLS_INTEGER;
                    v_set_menge ARTIKEL_SET_POS.ANZAHL%type;
                    vr_ar       ARTIKEL%ROWTYPE;
                    vr_set      ARTIKEL_SET%rowtype;
                    vr_vert     AUFTRAG_VERTEILUNG%ROWTYPE;
                    vr_vert_pos AUFTRAG_VERTEIL_POS%ROWTYPE;
                  begin
                    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_auf.REF='||cr_auf.REF);

                    --Prüfen ob es für den Auftrag bereits einen Verteil-Eintrag gibt
                    begin
                      select * into vr_vert from AUFTRAG_VERTEILUNG where REF_BATCHLAUF=pRefBatch and REF_AUF_KOPF=cr_auf.REF;

                      exception
                        when no_data_found then
                          vr_vert := null;
                        when others then
                          raise;
                    end;

                    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_vert.REF='||vr_vert.REF);

                    if (vr_vert.REF is null) then
                      --Wenn nicht, dann wird einer angelegt
                      if (vr_bat_cfg.BATCH_AUFTRAG_ART in ('AUF')) then
                        declare
                          v_fach_nr AUFTRAG_VERTEILUNG.FACH_NR%type;
                        begin
                          select max (FACH_NR) into v_fach_nr from AUFTRAG_VERTEILUNG where REF_BATCHLAUF=pRefBatch;

                          if (v_fach_nr is null) then
                            v_fach_nr := 1;
                          else
                            v_fach_nr := v_fach_nr + 1;
                          end if;

                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_fach_nr='||v_fach_nr);

                          update AUFTRAG set KOMM_LE_FACH=v_fach_nr where REF=cr_auf.REF;

                          insert into AUFTRAG_VERTEILUNG
                              (REF_BATCHLAUF, REF_AUF_KOPF, FACH_NR)
                            values
                              (pRefBatch, cr_auf.REF, v_fach_nr)
                            returning REF into vr_vert.REF;
                        end;
                      else
                        insert into AUFTRAG_VERTEILUNG
                            (REF_BATCHLAUF, REF_AUF_KOPF)
                          values
                            (pRefBatch, cr_auf.REF)
                          returning REF into vr_vert.REF;
                      end if;

                      --Und auch gleich die Verteilpos für alle Auftragspositionen, die auch in der Kommissionierung enthalten sind
                      for cr_auf_pos in (select * from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF and REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID)) loop
                        select * into vr_ar from ARTIKEL where REF=cr_auf_pos.REF_AR;

                        if (vr_ar.REF_ARTIKEL_SET is not null) then
                          select * into vr_set from ARTIKEL_SET where REF=vr_ar.REF_ARTIKEL_SET;
                        else
                          vr_set := null;
                        end if;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_auf_pos : REF='||cr_auf_pos.REF||', MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', MENGE_GESAMT='||cr_auf_pos.MENGE_GESAMT||', vr_ar.REF_ARTIKEL_SET='||vr_ar.REF_ARTIKEL_SET);

                        --Bei Setartikel ist die zu verteilende Menge=Menge * Setinhalt
                        if (vr_ar.REF_ARTIKEL_SET is not null) and (nvl (vr_set.OPT_KOMM_NO_SPLIT, '0') = '0') then
                          select sum (ANZAHL) into v_set_menge from ARTIKEL_SET_POS where REF_SET=vr_ar.REF_ARTIKEL_SET;

                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_menge='||v_set_menge);

                          v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));

                          if (nvl (v_set_menge, 0) > 0) then
                            v_vpe := v_vpe * v_set_menge;
                          end if;
                        else
                          v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));
                        end if;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_vpe='||v_vpe);

                        if (cr_auf_pos.GEWICHT_SOLL is not null) then
                          v_gw  := (nvl (cr_auf_pos.GEWICHT_SOLL, 0) - nvl (cr_auf_pos.GEWICHT_GESAMT, 0));
                        elsif (cr_auf_pos.REF_AR_EINHEIT is not null) then
                          select NETTO_GEWICHT * v_vpe into v_gw from ARTIKEL_EINHEIT where REF=cr_auf_pos.REF_AR_EINHEIT;
                        else
                          begin
                            select NETTO_GEWICHT * v_vpe into v_gw from ARTIKEL_EINHEIT where REF_AR=cr_auf_pos.REF_AR and REF_EINHEIT=cr_auf_pos.REF_EINHEIT and ROWNUM=1;

                            exception
                              when no_data_found then
                                v_gw := null;
                              when others then
                                raise;
                          end;
                        end if;

                        if (v_vpe > 0) then
                          select count (*) into v_count from AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=pRefBatch and REF_AUF_POS=cr_auf_pos.REF;

                          if (v_count = 0) then
                            insert into AUFTRAG_BATCHLAUF_POS
                                (REF_BATCHLAUF, REF_AUF_POS, MENGE_BATCH, GEWICHT_BATCH)
                              values
                                (pRefBatch, cr_auf_pos.REF, v_vpe, v_gw);
                          end if;
                        end if;

                        begin
                          select * into vr_vert_pos from AUFTRAG_VERTEIL_POS where REF_VERTEILUNG=vr_vert.REF and REF_AUF_POS=cr_auf_pos.REF;

                          exception
                            when no_data_found then
                              vr_vert_pos := null;
                            when others then
                              raise;
                        end;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> vr_vert_pos.REF='||vr_vert_pos.REF);

                        if (vr_vert_pos.REF is null) then
                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> insert AUFTRAG_VERTEIL_POS : MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', MENGE_GESAMT='||cr_auf_pos.MENGE_GESAMT);

                          --Wenn nicht, dann wird er angelegt
                          insert into AUFTRAG_VERTEIL_POS
                              (REF_VERTEILUNG, REF_AUF_POS, MENGE_SOLL, MENGE_VERTEILT)
                            values
                              (vr_vert.REF, cr_auf_pos.REF, cr_auf_pos.MENGE_SOLL, cr_auf_pos.MENGE_GESAMT)
                            returning REF into vr_vert_pos.REF;

                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> vr_vert_pos.REF='||vr_vert_pos.REF);
                        end if;
                      end loop;
                    else
                      --Für alle Auftragspositionen prüfen, ob es auch die Verteilpos gibt
                      for cr_auf_pos in (select * from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF and REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID)) loop
                        select * into vr_ar from ARTIKEL where REF=cr_auf_pos.REF_AR;

                        if (vr_ar.REF_ARTIKEL_SET is not null) then
                          select * into vr_set from ARTIKEL_SET where REF=vr_ar.REF_ARTIKEL_SET;
                        else
                          vr_set := null;
                        end if;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': cr_auf_pos : REF='||cr_auf_pos.REF||', MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', MENGE_GESAMT='||cr_auf_pos.MENGE_GESAMT||', vr_ar.REF_ARTIKEL_SET='||vr_ar.REF_ARTIKEL_SET);

                        --Bei Setartikel ist die zu verteilende Menge=Menge * Setinhalt
                        if (vr_ar.REF_ARTIKEL_SET is not null) and (nvl (vr_set.OPT_KOMM_NO_SPLIT, '0') = '0') then
                          select sum (ANZAHL) into v_set_menge from ARTIKEL_SET_POS where REF_SET=vr_ar.REF_ARTIKEL_SET;

                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_set_menge='||v_set_menge);

                          v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));

                          if (nvl (v_set_menge, 0) > 0) then
                            v_vpe := v_vpe * v_set_menge;
                          end if;
                        else
                          v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));
                        end if;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_vpe='||v_vpe);

                        if (cr_auf_pos.GEWICHT_SOLL is not null) then
                          v_gw  := (nvl (cr_auf_pos.GEWICHT_SOLL, 0) - nvl (cr_auf_pos.GEWICHT_GESAMT, 0));
                        elsif (cr_auf_pos.REF_AR_EINHEIT is not null) then
                          select NETTO_GEWICHT * v_vpe into v_gw from ARTIKEL_EINHEIT where REF=cr_auf_pos.REF_AR_EINHEIT;
                        else
                          begin
                            select NETTO_GEWICHT * v_vpe into v_gw from ARTIKEL_EINHEIT where REF_AR=cr_auf_pos.REF_AR and REF_EINHEIT=cr_auf_pos.REF_EINHEIT and ROWNUM=1;

                            exception
                              when no_data_found then
                                v_gw := null;
                              when others then
                                raise;
                          end;
                        end if;

                        if (v_vpe > 0) then
                          select count (*) into v_count from AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=pRefBatch and REF_AUF_POS=cr_auf_pos.REF;

                          if (v_count = 0) then
                            insert into AUFTRAG_BATCHLAUF_POS
                                (REF_BATCHLAUF, REF_AUF_POS, MENGE_BATCH, GEWICHT_BATCH)
                              values
                                (pRefBatch, cr_auf_pos.REF, v_vpe, v_gw);
                          end if;
                        end if;

                        begin
                          select * into vr_vert_pos from AUFTRAG_VERTEIL_POS where REF_VERTEILUNG=vr_vert.REF and REF_AUF_POS=cr_auf_pos.REF;

                          exception
                            when no_data_found then
                              vr_vert_pos := null;
                            when others then
                              raise;
                        end;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_vert_pos.REF='||vr_vert_pos.REF);

                        if (vr_vert_pos.REF is null) then
                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> insert AUFTRAG_VERTEIL_POS : MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', MENGE_GESAMT='||cr_auf_pos.MENGE_GESAMT);

                          --Wenn nicht, dann wird er angelegt
                          insert into AUFTRAG_VERTEIL_POS
                              (REF_VERTEILUNG, REF_AUF_POS, MENGE_SOLL, MENGE_VERTEILT)
                            values
                              (vr_vert.REF, cr_auf_pos.REF, cr_auf_pos.MENGE_SOLL, cr_auf_pos.MENGE_GESAMT)
                            returning REF into vr_vert_pos.REF;

                          BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> vr_vert_pos.REF='||vr_vert_pos.REF);
                        end if;
                      end loop;
                    end if;
                  end;
                end loop;
              end if;

              --Alle reservierten Bestände werden zu Kommissionierpositionen
              if (vr_bat_cfg.ABLAUF_ART = 'WA') then
                res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, pRefBatch, oResID, 'PP', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, pFlagKommBestand, pMaxKommVPE, oErrorCode, oErrorText);
              else
                res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, pRefBatch, oResID, 'BAT', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, pFlagKommBestand, pMaxKommVPE, oErrorCode, oErrorText);
              end if;
            end if;

            /*Wird nicht mehr benötigt
            for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
              if (res = 0) and (oErrorCode = 0) then
                res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_SPED_DFUE (cr_auf.REF, oErrorCode, oErrorText);
              end if;

              exit when (res <> 0) or (oErrorCode <> 0);
            end loop;
            */

            if (res = 0) then
              update AUFTRAG_BATCHLAUF set STATUS='AKT', STATUS_KOMM='PLA' where REF=pRefBatch;
            end if;
          end if;
        end if;
      end if;
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oResID='||oResID);
  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_PLANEN    (pRefMand        in  MANDANT.REF%TYPE,
                              pRefLager       in  LAGER.REF%TYPE,
                              pKommDate       in  date,
                              pBatchArt       in  varchar2,
                              pBatchGruppe    in  AUFTRAG_BATCHLAUF.BATCH_GRUPPE%TYPE,
                              pMinAufAnz      in  INTEGER,
                              pMaxAufAnz      in  INTEGER,
                              pMaxAufVPEAnz   in  INTEGER,
                              pFlagFehlmengen in  VARCHAR2,
                              pMHDToleranz    in  INTEGER,
                              pKommBenRef     in  SYS_BEN.REF%TYPE,
                              pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                              oRefBatch       out AUFTRAG_BATCHLAUF.REF%TYPE,
                              oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                              oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  v_ref_cfg   BATCHLAUF_CONFIG.REF%type;

  res	        NUMBER;
begin
  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLAUF_PLANEN');

  BASE_TRACING.TraceOutput (2,'pBatchArt   ='||pBatchArt);

  select REF into v_ref_cfg from BATCHLAUF_CONFIG where STATUS='AKT' and BATCH_AUFTRAG_ART=pBatchArt and (REF_LAGER=pRefLager or (REF_LAGER is null and (REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=pRefLager))));

  res := BATCHLAUF_CONFIG_PLANEN (pRefMand, pRefLager, pKommDate, v_ref_cfg, null, pBatchGruppe, pMinAufAnz, pMaxAufAnz, pMaxAufVPEAnz, null, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, null, oRefBatch, oResID, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BATCHLAUF_CONFIG_PLANEN
--*  Author            : Stefan Graf
--*  Datum             : 06.08.2014
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_CONFIG_PLANEN (pRefMand        in  MANDANT.REF%TYPE,
                                  pRefLager       in  LAGER.REF%TYPE,
                                  pKommDate       in  date,
                                  pRefBatchConfig in  BATCHLAUF_CONFIG.REF%type,
                                  pBatchGruppe    in  AUFTRAG_BATCHLAUF.BATCH_GRUPPE%TYPE,
                                  pMinAufAnz      in  INTEGER,
                                  pMaxAufAnz      in  INTEGER,
                                  pMaxAufVPEAnz   in  INTEGER,
                                  pMaxBatchVPEAnz in  INTEGER,
                                  pFlagFehlmengen in  VARCHAR2,
                                  pMHDToleranz    in  INTEGER,
                                  pKommBenRef     in  SYS_BEN.REF%TYPE,
                                  pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                                  oRefBatch       out AUFTRAG_BATCHLAUF.REF%TYPE,
                                  oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                                  oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  res	        NUMBER;

begin
  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLAUF_CONFIG_PLANEN');

  res := BATCHLAUF_CONFIG_PLANEN (pRefMand, pRefLager, pKommDate, pRefBatchConfig, null, pBatchGruppe, pMinAufAnz, pMaxAufAnz, pMaxAufVPEAnz, pMaxBatchVPEAnz, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, null, oRefBatch, oResID, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;


--*****************************************************************************
--*  Function Name     : BATCHLAUF_CONFIG_PLANEN
--*  Author            : Stefan Graf
--*  Datum             : 26.09.2014
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_CONFIG_PLANEN (pRefMand        in  MANDANT.REF%TYPE,
                                  pRefLager       in  LAGER.REF%TYPE,
                                  pKommDate       in  date,
                                  pRefBatchConfig in  BATCHLAUF_CONFIG.REF%type,
                                  pRefSped        in  SPEDITIONEN.REF%type,
                                  pBatchGruppe    in  AUFTRAG_BATCHLAUF.BATCH_GRUPPE%TYPE,
                                  pMinAufAnz      in  INTEGER,
                                  pMaxAufAnz      in  INTEGER,
                                  pMaxAufVPEAnz   in  INTEGER,
                                  pMaxBatchVPEAnz in  INTEGER,
                                  pFlagFehlmengen in  VARCHAR2,
                                  pMHDToleranz    in  INTEGER,
                                  pKommBenRef     in  SYS_BEN.REF%TYPE,
                                  pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                                  oRefBatch       out AUFTRAG_BATCHLAUF.REF%TYPE,
                                  oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                                  oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  res	        NUMBER;

begin
  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLAUF_CONFIG_PLANEN');

  res := BATCHLAUF_CONFIG_PLANEN (pRefMand, pRefLager, pKommDate, pRefBatchConfig, pRefSped, pBatchGruppe, pMinAufAnz, pMaxAufAnz, pMaxAufVPEAnz, pMaxBatchVPEAnz, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, null, oRefBatch, oResID, oErrorCode, oErrorText);

  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     : BATCHLAUF_CONFIG_PLANEN
--*  Author            : Stefan Graf
--*  Datum             : 13.11.2014
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function BATCHLAUF_CONFIG_PLANEN (pRefMand        in  MANDANT.REF%TYPE,
                                  pRefLager       in  LAGER.REF%TYPE,
                                  pKommDate       in  date,
                                  pRefBatchConfig in  BATCHLAUF_CONFIG.REF%type,
                                  pRefSped        in  SPEDITIONEN.REF%type,
                                  pBatchGruppe    in  AUFTRAG_BATCHLAUF.BATCH_GRUPPE%TYPE,
                                  pMinAufAnz      in  INTEGER,
                                  pMaxAufAnz      in  INTEGER,
                                  pMaxAufVPEAnz   in  INTEGER,
                                  pMaxBatchVPEAnz in  INTEGER,
                                  pFlagFehlmengen in  VARCHAR2,
                                  pMHDToleranz    in  INTEGER,
                                  pKommBenRef     in  SYS_BEN.REF%TYPE,
                                  pKommGrpRef     in  SYS_BEN_GRP.REF%TYPE,
                                  pMaxKommVPE     in  integer,
                                  oRefBatch       out AUFTRAG_BATCHLAUF.REF%TYPE,
                                  oResID          out LAGER_RES_BESTAND.RES_ID%TYPE,
                                  oErrorCode      OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_tour       WA_REL_TOUR%ROWTYPE;
  vr_auf        AUFTRAG%ROWTYPE;
  vr_av         AUFTRAG_VERSAND%ROWTYPE;
  vr_auf_adr    AUFTRAG_ADR%ROWTYPE;
  vr_bat_cfg    BATCHLAUF_CONFIG%ROWTYPE;
  vr_logi_param WARENEMPF_REL_KOMM%ROWTYPE;
  vr_bat_plan   AUFTRAG_ART_PLANUNG%ROWTYPE;

  v_ref_wa    WARENAUSGANG.REF%TYPE;

  v_tol       INTEGER;
  v_okflag    INTEGER;
  v_leer_komm INTEGER;

  v_ref_plan  TMP_RES_PLAN.REF%type;
  v_auf_id    LAGER_RES_BESTAND.RES_ID%TYPE;

  v_count     PLS_integer;
  v_vpe_anz   PLS_integer;
  v_vpe_count PLS_integer;
  v_auf_count PLS_integer;

  v_ok        BOOLEAN;
  v_stop      BOOLEAN;

  pFlagKommBestand  integer;  --als Parameter definieren.

  res	        NUMBER;
begin
  res := 0;

  oRefBatch := null;

  pFlagKommBestand := 1;  --als Parameter definieren.
  oErrorCode := 0;
  oErrorText := NULL;

  lTraceLevel := BASE_TRACING.GETTRACELEVEL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.BATCHLAUF_CONFIG_PLANEN');
  --if (lTraceLevel >= 2) then
    BASE_TRACING.TraceOutput (2,'pRefMand       ='||pRefMand);
    BASE_TRACING.TraceOutput (2,'pRefLager      ='||pRefLager);
    BASE_TRACING.TraceOutput (2,'pKommDate      ='||to_char (pKommDate, 'DD.MM.YYYY'));
    BASE_TRACING.TraceOutput (2,'pRefBatchConfig='||pRefBatchConfig);
    BASE_TRACING.TraceOutput (2,'pRefSped       ='||pRefSped);
    BASE_TRACING.TraceOutput (2,'pBatchGruppe   ='||pBatchGruppe);
    BASE_TRACING.TraceOutput (0,'pMinAufAnz     ='||pMinAufAnz);
    BASE_TRACING.TraceOutput (0,'pMaxAufAnz     ='||pMaxAufAnz);
    BASE_TRACING.TraceOutput (0,'pMaxAufVPEAnz  ='||pMaxAufVPEAnz);
    BASE_TRACING.TraceOutput (0,'pMaxBatchVPEAnz='||pMaxBatchVPEAnz);
    BASE_TRACING.TraceOutput (2,'pFlagFehlmengen='||pFlagFehlmengen);
    BASE_TRACING.TraceOutput (2,'pMHDToleranz   ='||pMHDToleranz);
    BASE_TRACING.TraceOutput (2,'pKommBenRef    ='||pKommBenRef);
    BASE_TRACING.TraceOutput (2,'pKommGrpRef    ='||pKommGrpRef);
    BASE_TRACING.TraceOutput (2,'pMaxKommVPE    ='||pMaxKommVPE);
  --end if;

  select * into vr_bat_cfg from BATCHLAUF_CONFIG where REF=pRefBatchConfig;

  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': vr_bat_cfg: BATCH_AUFTRAG_ART='||vr_bat_cfg.BATCH_AUFTRAG_ART||', OPT_SINGLE_POS_ORDER='||vr_bat_cfg.OPT_SINGLE_POS_ORDER||', OPT_SPERRGUT='||vr_bat_cfg.OPT_SPERRGUT||', OPT_SPERRGUT_ONLY='||vr_bat_cfg.OPT_SPERRGUT_ONLY||', OPT_TEIL_PLANUNG='||vr_bat_cfg.OPT_TEIL_PLANUNG);

  if (vr_bat_cfg.BATCH_AUFTRAG_ART = 'SPLIT') then
    oErrorCode := 4;
    oErrorText := 'Mehrteilig Batchläufte können hier nicht geplant werden';
  elsif (vr_bat_cfg.AUFTRAG_PLAN_ART='ARTIKEL') then
    res := PA_AUFTRAG_BATCHLAUF_PLANEN.BATCHLAUF_PLANEN_ARTIKEL (pRefMand, pRefLager, pKommDate, pRefBatchConfig, pRefSped, pBatchGruppe, pMinAufAnz, pMaxAufAnz, pMaxAufVPEAnz, pMaxBatchVPEAnz, pFlagFehlmengen, pMHDToleranz, pKommBenRef, pKommGrpRef, pMaxKommVPE, oRefBatch, oResID, oErrorCode, oErrorText);
  else
    delete from TMP_VOR_RES;

    if (pFlagFehlmengen = '2') then
      v_leer_komm := 1;
    else
      v_leer_komm := 0;
    end if;

    v_auf_count := 0;
    v_vpe_count := 0;

    delete from TMP_PLAN_RES_ID;

    select SEQ_ID.NEXTVAL into oResID from dual;
    insert into TMP_PLAN_RES_ID (RES_ID) values (oResID);

    BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': oResID='||oResID);

    SetKommPlanLB (pRefMand, pRefLager, nvl (vr_bat_cfg.AUFTRAG_PLAN_ART, 'BAT-'||vr_bat_cfg.BATCH_AUFTRAG_ART), pRefBatchConfig);

    delete from TMP_RES_PLAN_AUFTRAG;
    delete from TMP_RES_PLAN_BESTAND_USE;

    res := BUILD_RES_PLANUNG (vr_bat_cfg.REF, pRefSped, nvl (vr_bat_cfg.MAX_AUF_ANZ, pMaxAufAnz), v_ref_plan);

    if (vr_bat_cfg.REF_ART_PLANUNG is not null) then
      select * into vr_bat_plan from AUFTRAG_ART_PLANUNG where REF=vr_bat_cfg.REF_ART_PLANUNG;
    else
      begin
        select * into vr_bat_plan from (select * from AUFTRAG_ART_PLANUNG aap where (KOMM_ART is null or KOMM_ART='BAT')
         and REF_MAND=pRefMand and REF_LAGER=pRefLager  and aap.Auftrag_Art = vr_bat_cfg.BATCH_AUFTRAG_ART order by KOMM_ART nulls last) where ROWNUM=1;

        exception
          when no_data_found then
            vr_bat_plan := null;
          when others then
            raise;
      end;
    end if;

    --Keine Voll-Paletten planen
    vr_bat_plan.OPT_VOLL_PAL_PLAN := '0';

    select count (*) into v_count from TMP_RES_PLAN_AUFTRAG plan where plan.REF_PLAN=v_ref_plan and plan.SUM_VPE > 0;

    BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': v_count='||v_count||', MinAuf='||pMinAufAnz);

    if (pMinAufAnz is not null and pMinAufAnz > 1 and (v_count < pMinAufAnz)) then
      BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': Too few plannable orders');
    else
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_bat_plan: REF='||vr_bat_plan.REF||', OPT_KOMM_ART='||vr_bat_plan.OPT_KOMM_ART);
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> sysdate='||sysdate||', pRefMand='||pRefMand||',vr_bat_cfg.ART_SELECT_PLANUNG='||vr_bat_cfg.ART_SELECT_PLANUNG);
      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> vr_bat_cfg.REF_SUB_MAND='||vr_bat_cfg.REF_SUB_MAND||', pRefLager='||pRefLager||',pKommDate='||pKommDate );

      for cr_plan_auf in (select
                             auf.REF, plan.REF_PLAN
                           from
                             AUFTRAG auf
                             inner join TMP_RES_PLAN_AUFTRAG plan on (plan.REF_PLAN=v_ref_plan and plan.REF_AUF_KOPF=auf.REF)
                             inner join AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=auf.REF_ART_PLANUNG)
                             left outer join AUFTRAG_BACKLOG back on (back.REF_AUF_KOPF=auf.REF)
                           where
                             --plan.REF_PLAN is not null and
                             plan.SUM_VPE > 0 and
                             auf.REF_BATCHLAUF is null and
                             auf.STATUS in ('ANG') and
                             (back.NEXT_CHECK is null or back.NEXT_CHECK < sysdate) and
                             (nvl (aufplan.OPT_BATCH_PLANUNG, '0') = '1') and
                             auf.REF_MAND=pRefMand and
                             (vr_bat_cfg.REF_SUB_MAND is null or (auf.REF_SUB_MAND=vr_bat_cfg.REF_SUB_MAND)) and
                             auf.REF_LAGER=pRefLager and
                             auf.KOMM_DATUM=pKommDate
                           order by
                             plan.REF_PLAN nulls last,
                             auf.PRIO_LAGER desc nulls last,
                             auf.PRIO desc nulls last,
                             case
                               when (nvl (vr_bat_cfg.ART_SELECT_PLANUNG, '0') = '2') then
                                 auf.PROCESS_DATUM
                               when (nvl (vr_bat_cfg.ART_SELECT_PLANUNG, '0') = '1') then
                                 auf.BESTELL_DATUM
                               else
                                 auf.AUFTRAG_DATUM
                             end asc,
                             auf.AUFTRAG_DATUM
                         ) loop

        v_vpe_anz := null;

        vr_auf_adr := null;

        select * into vr_auf from AUFTRAG where REF=cr_plan_auf.REF;
        select * into vr_av from AUFTRAG_VERSAND where REF_AUF_KOPF=cr_plan_auf.REF;

        BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||'-> vr_auf: REF_PLAN='||cr_plan_auf.REF_PLAN||', AUFTRAGSART='||vr_auf.AUFTRAGSART||', AUFTRAG_NR='||vr_auf.AUFTRAG_NR||', SUM_VPE='||vr_auf.SUM_VPE||', vr_av.VERSAND_ART='||vr_av.VERSAND_ART);

        v_ok := False;

        if (pMaxAufVPEAnz is null or (nvl (vr_auf.SUM_VPE, 0) <= pMaxAufVPEAnz)) then
          v_ok := true;
        end if;

        if (v_ok) then
          --Prüfen, ob die Auftragsart in der Includeliste steht
          if (vr_bat_cfg.INCLUDE_AUFTRAG_ART is not null) and (instr (vr_bat_cfg.INCLUDE_AUFTRAG_ART, vr_auf.AUFTRAGSART) = 0) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': INCLUDE_AUFTRAG_ART');
            v_ok := False;
          end if;

          --Prüfen, ob die Auftragsart in der Excludeliste steht
          if v_ok and (vr_bat_cfg.EXCLUDE_AUFTRAG_ART is not null) and (instr (vr_bat_cfg.EXCLUDE_AUFTRAG_ART, vr_auf.AUFTRAGSART) > 0) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': EXCLUDE_AUFTRAG_ART');
            v_ok := False;
          end if;
        end if;

        if (v_ok) then
          --Prüfen, ob die Versandart in der Includeliste steht
          if (vr_bat_cfg.INCLUDE_VERSAND_ART is not null) and (vr_av.VERSAND_ART is null or (instr (vr_bat_cfg.INCLUDE_VERSAND_ART, ';'||vr_av.VERSAND_ART||';') = 0)) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': INCLUDE_VERSAND_ART');
            v_ok := False;
          end if;

          --Prüfen, ob die Versandart in der Excludeliste steht
          if v_ok and (vr_bat_cfg.EXCLUDE_VERSAND_ART is not null) and vr_av.VERSAND_ART is not null and (instr (vr_bat_cfg.EXCLUDE_VERSAND_ART, ';'||vr_av.VERSAND_ART||';') > 0) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': EXCLUDE_VERSAND_ART');
            v_ok := False;
          end if;
        end if;

        if (v_ok) and (vr_auf.REF_SUB_MAND is not null) then
          --Prüfen, ob die Auftragsart in der Includeliste steht
          if (vr_bat_cfg.INCLUDE_REF_SUB_MAND is not null) and (instr (vr_bat_cfg.INCLUDE_REF_SUB_MAND, ';'||vr_auf.REF_SUB_MAND||';') = 0) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': INCLUDE_REF_SUB_MAND');
            v_ok := False;
          end if;

          --Prüfen, ob die Auftragsart in der Excludeliste steht
          if v_ok and (vr_bat_cfg.EXCLUDE_REF_SUB_MAND is not null) and (instr (vr_bat_cfg.EXCLUDE_REF_SUB_MAND, ';'||vr_auf.REF_SUB_MAND||';') > 0) then
            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': EXCLUDE_REF_SUB_MAND');
            v_ok := False;
          end if;
        end if;

        if (v_ok) and (vr_bat_cfg.OPT_SINGLE_POS_ORDER in ('1','2')) then
          declare
            v_sum_set_vpe PLS_INTEGER;
            v_sum_auf_vpe PLS_INTEGER;
          begin
            v_sum_auf_vpe := 0;

            for cr_auf_pos in (select
                                   pos.MENGE_SOLL,ar.REF,ar.REF_ARTIKEL_SET,arset.OPT_KOMM_NO_SPLIT,pos.SET_POSITION
                                 from
                                   AUFTRAG_POS pos
                                   inner join ARTIKEL ar on (ar.REF=pos.REF_AR)
                                   left outer join ARTIKEL_SET arset on (arset.REF=ar.REF_ARTIKEL_SET)
                                 where
                                   nvl (pos.MENGE_SOLL, 0) > 0 and
                                   pos.REF_AUF_KOPF=vr_auf.REF
                              ) loop
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> cr_auf_pos MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', REF_ARTIKEL_SET='||cr_auf_pos.REF_ARTIKEL_SET||', OPT_KOMM_NO_SPLIT='||cr_auf_pos.OPT_KOMM_NO_SPLIT||', SET_POSITION='||cr_auf_pos.SET_POSITION);

              if (cr_auf_pos.REF_ARTIKEL_SET is null) or (nvl (cr_auf_pos.OPT_KOMM_NO_SPLIT, '0') = '1') then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);

                v_sum_auf_vpe := v_sum_auf_vpe + cr_auf_pos.MENGE_SOLL;
              else
                --Aufgelöste Set-Positionen dürfen nicht mit gezählt werden
                if (nvl (cr_auf_pos.SET_POSITION, '0') = '1') then
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);

                  v_sum_set_vpe := 0;
                else
                  BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);

                  select sum (ANZAHL * cr_auf_pos.MENGE_SOLL) into v_sum_set_vpe from ARTIKEL_SET_POS where REF_SET=cr_auf_pos.REF_ARTIKEL_SET and nvl (ANZAHL, 0) > 0;
                end if;

                v_sum_auf_vpe := v_sum_auf_vpe + v_sum_set_vpe;
              end if;
            end loop;

            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> v_sum_auf_vpe='||v_sum_auf_vpe);

            if (vr_bat_cfg.OPT_SINGLE_POS_ORDER = '1') then
              if (v_sum_auf_vpe <> 1) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> OPT_SINGLE_POS_ORDER 1');
                v_ok := False;
              end if;
            elsif (vr_bat_cfg.OPT_SINGLE_POS_ORDER = '2') then
              if (v_sum_auf_vpe = 1) then
                BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> OPT_SINGLE_POS_ORDER 2');
                v_ok := False;
              end if;
            end if;
          end;
        end if;

        if (v_ok) and (pRefSped is not null) then
          --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
          select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap where (nvl (ap.REF_SPED, av.REF_SPED)=pRefSped) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=vr_auf.REF;

          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': REF_SPED v_count='||v_count);

          if (v_count = 0) then
            v_ok := False;
          end if;
        end if;

        if (v_ok) then
          --Prüfen, ob es Big Item Artikel in dem Auftrag gibt
          select
              count (*)
            into
              v_count
            from
              AUFTRAG_POS ap
              inner join ARTIKEL_EINHEIT ae on (ae.REF=ap.REF_AR_EINHEIT)
              left outer join ARTIKEL_EINHEIT_REL_LAGER ael on (ael.REF_AR_EINHEIT=ae.REF and REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=vr_auf.REF_LAGER))
            where
              ap.REF_AUF_KOPF=vr_auf.REF and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and (coalesce (ael.OPT_BIG_ITEM, ae.OPT_BIG_ITEM, '0')='1');

          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': OPT_BIG_ITEM v_count='||v_count);

          if (v_count = 0) and (nvl (vr_bat_cfg.OPT_BIG_ITEM_ONLY, '0') = '1') then
            v_ok := false;
          elsif (v_count > 0) and (nvl (vr_bat_cfg.OPT_BIG_ITEM, '0') = '0') then
            if (nvl (vr_bat_cfg.OPT_TEIL_PLANUNG, '0') = '0') then
              v_ok := false;
            end if;
          end if;
        end if;

        if (v_ok) then
          --Prüfen, ob es Sperrgut-Artikel in dem Auftrag gibt
          select
              count (*)
            into
              v_count
            from
              AUFTRAG_POS ap
              inner join ARTIKEL_EINHEIT ae on (ae.REF=ap.REF_AR_EINHEIT)
              left outer join ARTIKEL_EINHEIT_REL_LAGER ael on (ael.REF_AR_EINHEIT=ae.REF and REF_LOCATION=(select REF_LOCATION from LOCATION_REL_LAGER where REF_LAGER=vr_auf.REF_LAGER))
            where
              ap.REF_AUF_KOPF=vr_auf.REF and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and (coalesce (ael.OPT_SPERRGUT, ae.OPT_SPERRGUT, '0')='1');

          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': OPT_SPERRGUT v_count='||v_count);

          if (v_count = 0) and (nvl (vr_bat_cfg.OPT_SPERRGUT_ONLY, '0') = '1') then
            v_ok := false;
          elsif (v_count > 0) and (nvl (vr_bat_cfg.OPT_SPERRGUT, '0') = '0') then
            if (nvl (vr_bat_cfg.OPT_TEIL_PLANUNG, '0') = '0') then
              v_ok := false;
            end if;
          end if;
        end if;

        if (v_ok) then
          select * into vr_auf_adr from AUFTRAG_ADR where REF=vr_auf.REF_LIEFER_ADR;

          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_auf_adr.LAND_ISO='||vr_auf_adr.LAND_ISO||', vr_bat_cfg.INCLUDE_COUNTRY='||vr_bat_cfg.INCLUDE_COUNTRY||', vr_bat_cfg.EXCLUDE_COUNTRY='||vr_bat_cfg.EXCLUDE_COUNTRY);

          --Prüfen, ob das Land in der Includeliste steht
          if (vr_bat_cfg.INCLUDE_COUNTRY is not null) and (instr (vr_bat_cfg.INCLUDE_COUNTRY, nvl (vr_auf_adr.LAND_ISO, 'DE')) = 0) then
            v_ok := False;
          end if;

          --Prüfen, ob das Land in der Excludeliste steht
          if v_ok and (vr_bat_cfg.EXCLUDE_COUNTRY is not null) and (instr (vr_bat_cfg.EXCLUDE_COUNTRY, nvl (vr_auf_adr.LAND_ISO, 'DE')) > 0) then
            v_ok := False;
          end if;
        end if;

        if (v_ok) then
          BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': vr_bat_cfg.INCLUDE_SPEDITION='||vr_bat_cfg.INCLUDE_SPEDITION||', vr_bat_cfg.EXCLUDE_SPEDITION='||vr_bat_cfg.EXCLUDE_SPEDITION);

          --Prüfen, ob der Spediteur in der Includeliste steht
          if (vr_bat_cfg.INCLUDE_SPEDITION is not null) then
            --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
            --Änderung BL am 20.10.20 instr wurde vorher auf = 0 abgefragt

            select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap, SPEDITIONEN sped where (instr (vr_bat_cfg.INCLUDE_SPEDITION, ';'||sped.NAME||';')>0) and sped.REF=(nvl (ap.REF_SPED, av.REF_SPED)) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=vr_auf.REF;

            BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': INCLUDE_SPEDITION v_count='||v_count);

            if (v_count = 0) then
              v_ok := False;
            end if;
          end if;

          --Prüfen, ob der Spediteur in der Excludeliste steht
          if (v_ok and (vr_bat_cfg.EXCLUDE_SPEDITION is not null)) then
            --Prüfen, ob der Auftrag oder eine der Auftragspositionen für den Speditor ist
            select count (*) into v_count from AUFTRAG_VERSAND av, AUFTRAG_POS ap, SPEDITIONEN sped where (instr ( vr_bat_cfg.EXCLUDE_SPEDITION, ';'||sped.NAME||';') > 0) and sped.REF=(nvl (ap.REF_SPED, av.REF_SPED)) and av.REF_AUF_KOPF=ap.REF_AUF_KOPF and ap.REF_AUF_KOPF=vr_auf.REF;

            BASE_TRACING.TraceOutput (5,'EXCLUDE_SPEDITION v_count='||v_count);

            if (v_count > 0) then
              v_ok := False;
            end if;
          end if;
        end if;

        if (v_ok) then
          select SEQ_ID.NEXTVAL into v_auf_id from dual;
          insert into TMP_PLAN_RES_ID (RES_ID) values (v_auf_id);

          BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': vr_auf.REF='||vr_auf.REF||', v_auf_id='||v_auf_id);

          res := CHECK_BESTAND (vr_auf, v_auf_id, v_tol, GetKommOpt (vr_auf), vr_bat_plan, vr_bat_cfg, v_okflag, oErrorCode, oErrorText);
          BASE_TRACING.TRACETIMESTAMP (0, '#'||$$plsql_line||': res='||res||', v_auf_id='||v_auf_id||', v_okflag='||v_okflag);
          if (res = 0) then
            res := PA_SCHNITTSTELLE.Enqueueing (PA_SCHNITTSTELLE.IFC_QUEUE_AUFTRAG_STATUS, vr_auf.REF, oErrorCode, oErrorText);

            if (v_okflag = 1) then
              BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_leer_komm='||v_leer_komm);

              --alle Positionen des Auftrages durchgehen, bei denen kein Bestand vorhanden ist und diesen freigeben.
              --evtl. neuen Parameter festlegen dafür
              --Soll-MHD/Charge muss ermittelt werden, Plan-MHD/Charge ist leer.
              if (v_leer_komm = 1) then
                res := RES_AUFPOS_OHNE_BESTAND (vr_auf, null, v_auf_id, oErrorCode, oErrorText);
              end if;

              if (res = 0) and (oErrorCode = 0) then
                --Nur wenn min. eine Auftragsposition, die nicht Crossdock ist, erfüllt werden kann, wird eine Kommissionierung angelegt
                select count (*) into v_count from LAGER_RES_BESTAND r where r.RES_ID=v_auf_id and (select CROSSDOCK_BESTELL_NR from AUFTRAG_POS where REF=r.REF_AUF_POS) is null;

                if (v_count = 0) then
                  res := RESET_RESERVIERUNG (v_auf_id);
                else
                  select sum (MENGE_USED) into v_vpe_anz from LAGER_RES_BESTAND where RES_ID=v_auf_id;

                  update LAGER_RES_BESTAND set RES_ID=oResID where RES_ID=v_auf_id;

                  --Gilt auch für die Temptabelle
                  update TMP_KOMM_RES set RES_ID=oResID where RES_ID=v_auf_id;

                  if (oRefBatch is null) then
                    res := PA_AUFTRAG_BATCH.CREATE_BATCHLAUF (pRefMand, null, pRefLager, vr_bat_cfg.BATCH_AUFTRAG_ART, pKommDate, pBatchGruppe, oRefBatch, oErrorCode, oErrorText);

                    if (res = 0) then
                      update AUFTRAG_BATCHLAUF set REF_BATCHLAUF_CONFIG=vr_bat_cfg.REF, REF_ART_PLANUNG=vr_bat_plan.REF, PRIO=vr_bat_cfg.PRIO, REF_PLAN_SPED=pRefSped where REF=oRefBatch;
                    end if;
                  end if;

                  if (res = 0) then
                    if (nvl (vr_bat_cfg.OPT_TEIL_PLANUNG, '0') = '0') then
                      res := PA_AUFTRAG_BATCH.ADD_AUFTRAG_BATCHLAUF (oRefBatch, vr_auf.REF, oErrorCode, oErrorText);
                    else
                      for cr_auf_pos in (select * from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF and REF In (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID)) loop
                        res := PA_AUFTRAG_BATCH.ADD_AUFTRAG_POS_BATCHLAUF (oRefBatch, cr_auf_pos.REF, null, null, oErrorCode, oErrorText);

                        exit when (res <> 0);
                      end loop;
                    end if;

                    if (res = 0) then
                      v_vpe_count := v_vpe_count + v_vpe_anz;
                      v_auf_count := v_auf_count + 1;
                    end if;
                  end if;
                end if;
              end if;
            else
              res := RESET_RESERVIERUNG (v_auf_id);

              if (vr_bat_cfg.BACKLOG_RESET_TIME is not null) then
                declare
                  vr_back AUFTRAG_BACKLOG%rowtype;
                  v_date  DATE;
                begin
                  /*
                  if (vr_auf.BESTELL_DATUM < (sysdate - 30)) then
                    v_date := sysdate + 7;
                  elsif (vr_auf.BESTELL_DATUM < (sysdate - 7)) then
                    v_date := sysdate + 1;
                  else
                    v_date := sysdate + ((nvl (vr_bat_cfg.BACKLOG_RESET_TIME, 0) * 60) / 84600);
                  end if;
                  */
                  v_date := sysdate + ((nvl (vr_bat_cfg.BACKLOG_RESET_TIME, 0) * 60) / 84600);

                  begin
                    select * into vr_back from AUFTRAG_BACKLOG where REF_AUF_KOPF=vr_auf.REF;

                    exception
                      when no_data_found then
                        vr_back := null;
                      when others then
                        raise;
                  end;

                  if (vr_back.REF_AUF_KOPF is null) then
                    insert into AUFTRAG_BACKLOG (REF_AUF_KOPF,LAST_CHECK,NEXT_CHECK,BATCH_CHECK_COUNT) values (vr_auf.REF, sysdate, v_date, 1);
                  else
                    update AUFTRAG_BACKLOG set BATCH_CHECK_COUNT=nvl (BATCH_CHECK_COUNT,0) + 1, LAST_CHECK=sysdate, NEXT_CHECK=v_date where REF_AUF_KOPF=vr_auf.REF;
                  end if;
                end;
              end if;
            end if;
          end if;
        end if;

        exit when (res <> 0);

        v_stop := true;

        BASE_TRACING.TraceOutput (0, '#'||$$plsql_line||': oRefBatch='||oRefBatch||' v_vpe_count='||v_vpe_count||', v_auf_count='||v_auf_count||', v_vpe_anz='||v_vpe_anz);

        exit when (pMaxBatchVPEAnz is not null and v_vpe_count >= pMaxBatchVPEAnz);
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);
        exit when (pMaxAufAnz is not null and v_auf_count >= pMaxAufAnz);
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);
        exit when (vr_bat_cfg.MAX_AUF_ANZ is not null and v_auf_count >= vr_bat_cfg.MAX_AUF_ANZ);
        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line);

        v_stop := false;

        res := PA_TIMEOUT.CheckTimeout (true);
      end loop;

      if (res = 0) and (v_auf_count > 0) then
        if not (v_stop) and (nvl (pMinAufAnz, 1) > v_auf_count) then
          res := RESET_RESERVIERUNG (oResID);

          if (res = 0) then
            res := PA_AUFTRAG_BATCH.DELETE_BATCHLAUF (oRefBatch, oErrorCode, oErrorText);

            oRefBatch := null;
          end if;
        else
          for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
            delete from TMP_RES_PLAN_AUFTRAG where REF_PLAN=v_ref_plan and REF_AUF_KOPF=cr_auf.REF;

            for cr_auf_pos in (select * from AUFTRAG_POS where REF_AUF_KOPF=cr_auf.REF and REF In (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID)) loop
              --Die Vorreservierungen zurücksetzen
              delete from LAGER_VOR_RES_BESTAND where REF_AUF_POS=cr_auf_pos.REF;
              update AUFTRAG_POS set MENGE_VOR_RES=null where REF=cr_auf_pos.REF;
            end loop;

            res := FREIGEBEN (cr_auf, oResID, v_ref_wa, oErrorCode, oErrorText);

            exit when (res <> 0) or (oErrorCode <> 0);
          end loop;

          if (res = 0) and (oErrorCode = 0) Then
            update AUFTRAG_BATCHLAUF set COUNT_FREIGABE=nvl (COUNT_FREIGABE, 0) + 1 where REF=oRefBatch;

            BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> vr_bat_cfg.BATCH_AUFTRAG_ART='||vr_bat_cfg.BATCH_AUFTRAG_ART);

            if (vr_bat_cfg.BATCH_AUFTRAG_ART in ('AUF', 'SAMMEL', 'CROSS')) then
              for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
                BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||'-> cr_auf.REF='||cr_auf.REF);

                declare
                  v_set_menge ARTIKEL_SET_POS.ANZAHL%TYPE;
                  v_vpe       AUFTRAG_VERTEIL_POS.MENGE_SOLL%TYPE;
                  v_ver_ref   AUFTRAG_VERTEILUNG.REF%TYPE;
                  vr_set      ARTIKEL_SET%rowtype;
                  v_vert_pos  AUFTRAG_VERTEIL_POS.REF%TYPE;
                begin
                  select count (*) into v_count from AUFTRAG_VERTEILUNG where REF_BATCHLAUF=oRefBatch and REF_AUF_KOPF=cr_auf.REF;

                  if (v_count = 0) then
                    if (vr_bat_cfg.BATCH_AUFTRAG_ART in ('AUF')) then
                      declare
                        v_fach_nr AUFTRAG_VERTEILUNG.FACH_NR%type;
                      begin
                        select max (FACH_NR) into v_fach_nr from AUFTRAG_VERTEILUNG where REF_BATCHLAUF=oRefBatch;

                        if (v_fach_nr is null) then
                          v_fach_nr := 1;
                        else
                          v_fach_nr := v_fach_nr + 1;
                        end if;

                        BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_fach_nr='||v_fach_nr);

                        update AUFTRAG set KOMM_LE_FACH=v_fach_nr where REF=cr_auf.REF;

                        insert into AUFTRAG_VERTEILUNG
                            (REF_BATCHLAUF, REF_AUF_KOPF, FACH_NR)
                          values
                            (oRefBatch, cr_auf.REF, v_fach_nr)
                          returning REF into v_ver_ref;
                      end;
                    else
                      insert into AUFTRAG_VERTEILUNG
                          (REF_BATCHLAUF, REF_AUF_KOPF)
                        values
                          (oRefBatch, cr_auf.REF)
                        returning REF into v_ver_ref;
                    end if;

                    BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||'-> v_ver_ref='||v_ver_ref);

                    for cr_auf_pos in (select pos.REF,pos.MENGE_SOLL,pos.MENGE_GESAMT,ar.REF_ARTIKEL_SET from AUFTRAG_POS pos, ARTIKEL ar where ar.REF=pos.REF_AR and pos.REF_AUF_KOPF=cr_auf.REF and pos.REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID)) loop
                      if (cr_auf_pos.REF_ARTIKEL_SET is not null) then
                        select * into vr_set from ARTIKEL_SET where REF=cr_auf_pos.REF_ARTIKEL_SET;
                      else
                        vr_set := null;
                      end if;

                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': cr_auf_pos : REF='||cr_auf_pos.REF||', MENGE_SOLL='||cr_auf_pos.MENGE_SOLL||', MENGE_GESAMT='||cr_auf_pos.MENGE_GESAMT||', REF_ARTIKEL_SET='||cr_auf_pos.REF_ARTIKEL_SET||' OPT_KOMM_NO_SPLIT='||vr_set.OPT_KOMM_NO_SPLIT);

                      --Bei Setartikel ist die zu verteilende Menge=Menge * Setinhalt
                      if (cr_auf_pos.REF_ARTIKEL_SET is not null) and (nvl (vr_set.OPT_KOMM_NO_SPLIT, '0') = '0') then
                        select sum (ANZAHL) into v_set_menge from ARTIKEL_SET_POS where REF_SET=cr_auf_pos.REF_ARTIKEL_SET;

                        BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_set_menge='||v_set_menge);

                        v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));

                        if (nvl (v_set_menge, 0) > 0) then
                          v_vpe := v_vpe * v_set_menge;
                        end if;
                      else
                        v_vpe := (nvl (cr_auf_pos.MENGE_SOLL, 0) - nvl (cr_auf_pos.MENGE_GESAMT, 0));
                      end if;

                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_vpe='||v_vpe);

                      insert into AUFTRAG_VERTEIL_POS
                          (REF_VERTEILUNG, REF_AUF_POS, MENGE_SOLL, MENGE_VERTEILT)
                        values
                          (v_ver_ref, cr_auf_pos.REF, v_vpe, cr_auf_pos.MENGE_GESAMT)
                        returning REF into v_vert_pos;

                      BASE_TRACING.TraceOutput (5, '#'||$$plsql_line||': v_vert_pos='||v_vert_pos);
                    end loop;
                  end if;
                end;
              end loop;
            end if;

            --Alle reservierten Bestände werden zu Kommissionierpositionen
            res := PA_KOMM_PLANUNG.KOMM_PLANEN (null, oRefBatch, oResID, 'BAT', null, pKommBenRef, pKommGrpRef, vr_logi_param, vr_tour, pFlagKommBestand, pMaxKommVPE, oErrorCode, oErrorText);
          end if;

          /*Wird nicht mehr benötigt
          for cr_auf in (select * from AUFTRAG where REF in (select REF_AUF_KOPF from AUFTRAG_POS where REF in (select REF_AUF_POS from LAGER_RES_BESTAND where RES_ID=oResID))) loop
            if (res = 0) and (oErrorCode = 0) then
              res := PA_SCHNITTSTELLE.MELDE_AUFTRAG_SPED_DFUE (cr_auf.REF, oErrorCode, oErrorText);
            end if;

            exit when (res <> 0) or (oErrorCode <> 0);
          end loop;
          */

          if (res = 0) then
            update AUFTRAG_BATCHLAUF set STATUS='AKT', STATUS_KOMM='PLA', STATUS_VERT='BER' where REF=oRefBatch;

            res := PA_SCHNITTSTELLE.MELDE_BATCH_PLANUNG (oRefBatch, oErrorCode, oErrorText);
          end if;
        end if;
      end if;
    end if;

     /*Passt noch nicht ganz*/
    if (res = 0) then
      null;
    /*
      declare
        v_ref_err     FEHLER_LISTE.REF%type;
        v_ref_err_pos FEHLER_POS.REF%type;
        v_stat        FEHLER_POS.FEHLER_STATUS%type;
      begin
        for cr_auf in (select
                           auf.*
                         from
                           TMP_RES_PLAN_AUFTRAG plan
                           inner join AUFTRAG auf on (auf.REF=plan.REF_AUF_KOPF)
                         where
                           plan.REF_PLAN=v_ref_plan and
                           plan.SUM_VPE=0
                      ) loop
          if (cr_auf.REF_FEHLER is null) then
            insert into FEHLER_LISTE
                (REF_ID, FEHLER_STATUS, FEHLER_TEXT, ID_TYP)
              values
                (cr_auf.REF, 'PLAN_BESTAND', null, 'AUF')
              returning REF into v_ref_err;

            for cr_fehler in (select
                                  use.REF_AUF_POS, use.MENGE_USED, res.MENGE_FREI, pos.*
                                from
                                  TMP_RES_PLAN_BESTAND_USE use
                                  inner join TMP_RES_PLAN_BESTAND res on (res.REF=use.REF_RES)
                                  inner join AUFTRAG_POS pos on (pos.REF=use.REF_AUF_POS)
                                where
                                  use.REF_AUF_KOPF=cr_auf.REF) loop
              if (nvl (cr_fehler.MENGE_FREI, 0) = 0) then
                v_stat := 'FEHLWARE';
              else
                v_stat := 'BESTAND';
              end if;

              insert into FEHLER_POS
                   (REF_LISTE, REF_ID, REF_POS, FEHLER_STATUS, FEHLER_TEXT, SOLL_MENGE, FEHL_MENGE)
                values
                  (v_ref_err, cr_fehler.REF_AUF_POS, cr_fehler.POS_NR, v_stat, null, cr_fehler.MENGE_SOLL, cr_fehler.MENGE_USED)
                returning REF into v_ref_err_pos;

              update AUFTRAG_POS set REF_FEHLER_POS=v_ref_err_pos where REF=cr_fehler.REF_AUF_POS;
            end loop;

            update AUFTRAG set FEHLER_STATUS='PLAN_BESTAND', REF_FEHLER=v_ref_err where REF=cr_auf.REF;
          end if;

          if (vr_bat_cfg.BACKLOG_RESET_TIME is not null) then
            declare
              vr_back AUFTRAG_BACKLOG%rowtype;
              v_date  DATE;
            begin
              v_date := sysdate + ((nvl (vr_bat_cfg.BACKLOG_RESET_TIME, 0) * 60) / 84600);

              begin
                select * into vr_back from AUFTRAG_BACKLOG where REF_AUF_KOPF=cr_auf.REF;

                exception
                  when no_data_found then
                    vr_back := null;
                  when others then
                    raise;
              end;

              if (vr_back.REF_AUF_KOPF is null) then
                insert into AUFTRAG_BACKLOG (REF_AUF_KOPF,LAST_CHECK,NEXT_CHECK,BATCH_CHECK_COUNT) values (cr_auf.REF, sysdate, v_date, 1);
              else
                update AUFTRAG_BACKLOG set BATCH_CHECK_COUNT=nvl (BATCH_CHECK_COUNT,0) + 1, LAST_CHECK=sysdate, NEXT_CHECK=v_date where REF_AUF_KOPF=cr_auf.REF;
              end if;
            end;
          end if;
        end loop;
      end;
      */
    end if;
  end if;

  BASE_TRACING.TraceOutput (2, 'oRefBatch='||oRefBatch);
  BASE_TRACING.TraceOutput (2, 'oResID   ='||oResID);

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

--*****************************************************************************
--*  Function Name     :
--*  Author            : Stefan Graf
--*****************************************************************************
--*  Description       :
--*---------------------------------------------------------------------------
--*  Return Value      :
--*****************************************************************************
function CALC_LAGER_PRIO (pDatum in VARCHAR2, oErrorCode OUT INTEGER, oErrorText OUT nocopy VARCHAR2) RETURN INTEGER is

  aktlevel    PLS_INTEGER;

  vr_vers     AUFTRAG_VERSAND%ROWTYPE;

  v_prio      AUFTRAG.PRIO_LAGER%TYPE;

  v_soll_sum  AUFTRAG_POS.MENGE_SOLL%TYPE;

  v_pos_count INTEGER;

  aktdate     DATE;

  res	        NUMBER;
begin
  res := 0;

  oErrorCode := 0;
  oErrorText := NULL;

  aktlevel := BASE_TRACING.ENTERPROC ('PA_AUFTRAG_PLANEN.CALC_LAGER_PRIO');
  BASE_TRACING.TraceOutput (2,'pDatum ='||pDatum);

  if (pDatum is null) then
    aktdate := Trunc (sysdate);
  else
    aktdate := Trunc (TO_DATE (pDatum, 'DD.MM.YYYY'));
  end if;

  BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': aktdate ='||aktdate);

  for vr_auf in (select * from AUFTRAG where STATUS not in ('TRA', 'ABG', 'FIN') and KOMM_DATUM between aktdate and aktdate + 1) loop
    select * into vr_vers from AUFTRAG_VERSAND where REF_AUF_KOPF=vr_auf.REF;

    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': STATUS='||vr_auf.STATUS||', TOUR_NR='||vr_vers.TOUR_NR);

    if (vr_auf.KOMM_DATUM < aktdate) then
      v_prio := 100;
    else
      begin
        select PRIO into v_prio from (select * from WA_REL_TOUR where REF_MAND=vr_auf.REF_MAND and REF_LAGER=vr_auf.REF_LAGER and TOUR_NR=vr_vers.TOUR_NR order by PRIO desc) where ROWNUM=1;

        exception
          when no_data_found then
            v_prio := null;

          when others then
            raise;
      end;

      --Noch die Gesamtanzahl von VEs mit zur Prio addieren
      select count (*) into v_pos_count from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF;

      --Die Obergrenze liegt willkürlich bei 500 VEs ;-)
      if (v_pos_count >= 50) then
        v_prio := nvl (v_prio, 0) + 5;
      else
        v_prio := nvl (v_prio, 0) + floor ((5 / 50) * v_pos_count);
      end if;


      --Noch die Gesamtanzahl von VEs mit zur Prio addieren
      select SUM (MENGE_SOLL) into v_soll_sum from AUFTRAG_POS where REF_AUF_KOPF=vr_auf.REF;

      --Die Obergrenze liegt willkürlich bei 500 VEs ;-)
      if (v_soll_sum >= 500) then
        v_prio := nvl (v_prio, 0) + 5;
      else
        v_prio := nvl (v_prio, 0) + floor ((5 / 500) * v_soll_sum);
      end if;
    end if;

    BASE_TRACING.TraceOutput (3, '#'||$$plsql_line||': v_prio='||v_prio||', vr.PRIO_LAGER='||vr_auf.PRIO_LAGER);

    if (nvl (v_prio, 0) <> nvl (vr_auf.PRIO_LAGER, 0)) then
      res := PA_AUFTRAG.SET_AUFTRAG_PRIO (vr_auf.REF, v_prio, 'calc', oErrorCode, oErrorText);
    else
      if (vr_auf.REF_PACKLISTE is null) then
        for vr_komm in (select * from KOMM_KOPF where STATUS<>'ABG' and REF_AUFTRAG=vr_auf.REF) loop
          for vr_ta in (select * from TA where REF_KOMM_KOPF=vr_komm.REF) loop
            if (nvl (v_prio, 0) <> nvl (vr_ta.PRIO, 0)) then
              update TA set PRIO=v_prio where REF=vr_ta.REF;
            end if;
          end loop;
        end loop;
      else
        select PRIO into v_prio from AUFTRAG_PACKLISTE where REF=vr_auf.REF_PACKLISTE;

        for vr_komm in (select * from KOMM_KOPF where STATUS<>'ABG' and (REF_AUFTRAG=vr_auf.REF or REF_PACKLISTE=vr_auf.REF_PACKLISTE)) loop
          for vr_ta in (select * from TA where REF_KOMM_KOPF=vr_komm.REF) loop
            if (nvl (v_prio, 0) <> nvl (vr_ta.PRIO, 0)) then
              update TA set PRIO=v_prio where REF=vr_ta.REF;
            end if;
          end loop;
        end loop;
      end if;
    end if;

    exit when (res <> 0) or (oErrorCode <> 0);
  end loop;

  BASE_TRACING.TraceOutput (2, 'oErrorCode='||oErrorCode);
  BASE_TRACING.TraceOutput (2, 'oErrorText='||oErrorText);

	if (res = 0) and (oErrorCode <> 0) then res := 4; end if;
  BASE_TRACING.LEAVEPROC (res);

  return res;
end;

BEGIN
  whatpb := '$revision: 132.1 $' || '' ;
END PA_AUFTRAG_PLANEN;
/

