import { chromium } from "@playwright/test";

async function runHeadedTest() {
  const browser = await chromium.launch({
    headless: false, // Sichtbarer Browser
    slowMo: 100, // Langsamer für bessere Beobachtung
    devtools: true, // DevTools öffnen
  });

  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log("Starte Test mit sichtbarem Browser...");

    await page.goto("https://slwebtest.storelogix.de/login");
    await page.fill('input[aria-label="Benutzername"]', "EXTERNERMANDANT1");
    await page.fill('input[aria-label="Passwort"]', "321");
    await page.click('button:has-text("Anmelden")');

    await page.waitForTimeout(3000);

    console.log("Login versucht - Browser bleibt offen für Inspektion");
    console.log("Drücke Enter um fortzufahren...");

    // Warten auf Benutzereingabe
    await new Promise((resolve) => {
      process.stdin.once("data", () => resolve());
    });

    await page.goto("https://slwebtest.storelogix.de/orders");
    await page.waitForTimeout(2000);

    await page.goto("https://slwebtest.storelogix.de/stock");
    await page.waitForTimeout(2000);

    console.log("Test abgeschlossen!");
  } catch (error) {
    console.error("Fehler:", error.message);
  } finally {
    await browser.close();
  }
}

runHeadedTest();
