import axios from "axios";

class HttpPerformanceMetrics {
  constructor() {
    this.results = [];
  }

  addResult(
    testRun,
    loginTime,
    ordersTime,
    stocksTime,
    totalTime,
    errors,
    statusCodes
  ) {
    this.results.push({
      testRun,
      loginTime,
      ordersTime,
      stocksTime,
      totalTime,
      errors,
      statusCodes,
      timestamp: new Date().toISOString(),
    });
  }

  getStatistics() {
    if (this.results.length === 0) return null;

    const successful = this.results.filter((r) => r.errors.length === 0);
    const loginTimes = successful.map((r) => r.loginTime);
    const ordersTimes = successful.map((r) => r.ordersTime);
    const stocksTimes = successful.map((r) => r.stocksTime);
    const totalTimes = successful.map((r) => r.totalTime);

    return {
      totalRuns: this.results.length,
      successfulRuns: successful.length,
      failureRate: (
        ((this.results.length - successful.length) / this.results.length) *
        100
      ).toFixed(2),
      averageLoginTime: this.average(loginTimes),
      averageOrdersTime: this.average(ordersTimes),
      averageStocksTime: this.average(stocksTimes),
      averageTotalTime: this.average(totalTimes),
      minTotalTime: Math.min(...totalTimes),
      maxTotalTime: Math.max(...totalTimes),
      errors: this.results
        .filter((r) => r.errors.length > 0)
        .map((r) => r.errors)
        .flat(),
    };
  }

  average(arr) {
    return arr.length > 0
      ? (arr.reduce((a, b) => a + b, 0) / arr.length).toFixed(2)
      : 0;
  }

  printReport() {
    const stats = this.getStatistics();
    if (!stats) {
      console.log("Keine Testergebnisse verfügbar");
      return;
    }

    console.log("\n=== HTTP LASTTEST AUSWERTUNG ===");
    console.log(`Gesamte Tests: ${stats.totalRuns}`);
    console.log(`Erfolgreiche Tests: ${stats.successfulRuns}`);
    console.log(`Fehlerrate: ${stats.failureRate}%`);
    console.log(`\nDurchschnittliche Zeiten (ms):`);
    console.log(`  Login: ${stats.averageLoginTime}ms`);
    console.log(`  Orders-API: ${stats.averageOrdersTime}ms`);
    console.log(`  Stocks-API: ${stats.averageStocksTime}ms`);
    console.log(`  Gesamt: ${stats.averageTotalTime}ms`);
    console.log(
      `\nMin/Max Gesamtzeit: ${stats.minTotalTime}ms / ${stats.maxTotalTime}ms`
    );

    if (stats.errors.length > 0) {
      console.log(`\nFehler:`);
      stats.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
  }
}

async function runHttpTest(testNumber, metrics) {
  const startTime = Date.now();
  const errors = [];
  const statusCodes = {};

  try {
    const client = axios.create({
      baseURL: "https://slwebtest.storelogix.de",
      timeout: 10000,
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      },
    });

    const loginStart = Date.now();

    const loginResponse = await client.post("/api/auth/login", {
      username: "EXTERNERMANDANT1",
      password: "321",
    });

    statusCodes.login = loginResponse.status;

    if (loginResponse.status !== 200) {
      throw new Error(`Login fehlgeschlagen: Status ${loginResponse.status}`);
    }

    const authToken =
      loginResponse.data.token || loginResponse.headers["authorization"];
    if (authToken) {
      client.defaults.headers.common["Authorization"] = `Bearer ${authToken}`;
    }

    const cookies = loginResponse.headers["set-cookie"];
    if (cookies) {
      client.defaults.headers.common["Cookie"] = cookies.join("; ");
    }

    const loginTime = Date.now() - loginStart;

    const ordersStart = Date.now();
    const ordersResponse = await client.get("/api/orders");
    statusCodes.orders = ordersResponse.status;
    const ordersTime = Date.now() - ordersStart;

    const stocksStart = Date.now();
    const stocksResponse = await client.get("/api/stock");
    statusCodes.stocks = stocksResponse.status;
    const stocksTime = Date.now() - stocksStart;

    const totalTime = Date.now() - startTime;

    metrics.addResult(
      testNumber,
      loginTime,
      ordersTime,
      stocksTime,
      totalTime,
      errors,
      statusCodes
    );

    console.log(`HTTP Test ${testNumber}: Erfolgreich (${totalTime}ms)`);
  } catch (error) {
    const totalTime = Date.now() - startTime;
    errors.push(error.message);
    metrics.addResult(testNumber, 0, 0, 0, totalTime, errors, statusCodes);
    console.log(`HTTP Test ${testNumber}: Fehler - ${error.message}`);
  }
}

async function runHttpLoadTest() {
  const concurrency = 50; // Viel höhere Anzahl möglich
  const metrics = new HttpPerformanceMetrics();

  console.log(
    `Starte HTTP Load Test mit ${concurrency} parallelen Requests...`
  );

  const testPromises = [];
  for (let i = 1; i <= concurrency; i++) {
    testPromises.push(runHttpTest(i, metrics));
  }

  await Promise.all(testPromises);

  metrics.printReport();
}

runHttpLoadTest().catch(console.error);
