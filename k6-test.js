import http from "k6/http";
import { check, sleep } from "k6";
import { Rate } from "k6/metrics";

export let errorRate = new Rate("errors");

export let options = {
  stages: [
    { duration: "30s", target: 10 }, 
    { duration: "1m", target: 50 }, 
    { duration: "2m", target: 100 }, 
    { duration: "1m", target: 100 }, 
    { duration: "30s", target: 0 }, 
  ],
  thresholds: {
    errors: ["rate<0.1"], 
    http_req_duration: ["p(95)<2000"],
  },
};

export default function () {
  const baseUrl = "https://slwebtest.storelogix.de";

  const loginResponse = http.post(
    `${baseUrl}/login`,
    JSON.stringify({
      username: "EXTERNERMANDANT1",
      password: "321",
    }),
    {
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "k6 Load Test",
      },
    }
  );

  const loginSuccess = check(loginResponse, {
    "Login erfolgreich": (r) => r.status === 200,
    "Login Response Zeit < 1s": (r) => r.timings.duration < 1000,
  });

  if (!loginSuccess) {
    errorRate.add(1);
    return;
  }

  const authToken = loginResponse.json("token");
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "User-Agent": "k6 Load Test",
  };

  const ordersResponse = http.get(`${baseUrl}/orders`, { headers });
  check(ordersResponse, {
    "Orders Status 200": (r) => r.status === 200,
    "Orders Response Zeit < 500ms": (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  const stockResponse = http.get(`${baseUrl}/stock`, { headers });
  check(stockResponse, {
    "Stock Status 200": (r) => r.status === 200,
    "Stock Response Zeit < 500ms": (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  sleep(1);
}
