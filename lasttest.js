import { chromium } from "@playwright/test";

class PerformanceMetrics {
  constructor() {
    this.results = [];
  }

  addResult(testRun, loginTime, ordersTime, stocksTime, totalTime, errors) {
    this.results.push({
      testRun,
      loginTime,
      ordersTime,
      stocksTime,
      totalTime,
      errors,
      timestamp: new Date().toISOString(),
    });
  }

  getStatistics() {
    if (this.results.length === 0) return null;

    const successful = this.results.filter((r) => r.errors.length === 0);
    const loginTimes = successful.map((r) => r.loginTime);
    const ordersTimes = successful.map((r) => r.ordersTime);
    const stocksTimes = successful.map((r) => r.stocksTime);
    const totalTimes = successful.map((r) => r.totalTime);

    return {
      totalRuns: this.results.length,
      successfulRuns: successful.length,
      failureRate: (
        ((this.results.length - successful.length) / this.results.length) *
        100
      ).toFixed(2),
      averageLoginTime: this.average(loginTimes),
      averageOrdersTime: this.average(ordersTimes),
      averageStocksTime: this.average(stocksTimes),
      averageTotalTime: this.average(totalTimes),
      minTotalTime: Math.min(...totalTimes),
      maxTotalTime: Math.max(...totalTimes),
      errors: this.results
        .filter((r) => r.errors.length > 0)
        .map((r) => r.errors)
        .flat(),
    };
  }

  average(arr) {
    return arr.length > 0
      ? (arr.reduce((a, b) => a + b, 0) / arr.length).toFixed(2)
      : 0;
  }

  printReport() {
    const stats = this.getStatistics();
    if (!stats) {
      console.log("Keine Testergebnisse verfügbar");
      return;
    }

    console.log("\n=== PERFORMANCE AUSWERTUNG ===");
    console.log(`Gesamte Tests: ${stats.totalRuns}`);
    console.log(`Erfolgreiche Tests: ${stats.successfulRuns}`);
    console.log(`Fehlerrate: ${stats.failureRate}%`);
    console.log(`\nDurchschnittliche Zeiten (ms):`);
    console.log(`  Login: ${stats.averageLoginTime}ms`);
    console.log(`  Orders-Seite: ${stats.averageOrdersTime}ms`);
    console.log(`  Stocks-Seite: ${stats.averageStocksTime}ms`);
    console.log(`  Gesamt: ${stats.averageTotalTime}ms`);
    console.log(
      `\nMin/Max Gesamtzeit: ${stats.minTotalTime}ms / ${stats.maxTotalTime}ms`
    );

    if (stats.errors.length > 0) {
      console.log(`\nFehler:`);
      stats.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
  }
}

async function runSingleTest(testNumber, metrics) {
  const startTime = Date.now();
  const errors = [];
  let browser;

  try {
    browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    const loginStart = Date.now();
    await page.goto("https://slwebtest.storelogix.de/login");

    await page.evaluate(() => {
      localStorage.setItem(
        "tour-store",
        '{"state":{"showTour":false},"version":0}'
      );
    });

    await page.waitForLoadState("networkidle");

    await page.fill('input[aria-label="Benutzername"]', "EXTERNERMANDANT1");
    await page.fill('input[aria-label="Passwort"]', "321");

    await page.click('button:has-text("Anmelden")');

    await page.waitForTimeout(2000);

    const errorMessage = await page
      .locator("div.text-red-500.text-sm")
      .textContent()
      .catch(() => null);
    if (errorMessage) {
      console.log(
        `Test ${testNumber}: Login-Fehlermeldung erkannt: "${errorMessage}"`
      );
      throw new Error(`Login-Fehlermeldung: ${errorMessage}`);
    }

    const hasInvalidFields = await page
      .locator('input[aria-invalid="true"]')
      .count();
    if (hasInvalidFields > 0) {
      console.log(
        `Test ${testNumber}: ${hasInvalidFields} ungültige Eingabefelder erkannt`
      );
      const allErrors = await page
        .locator("div.text-red-500")
        .allTextContents();
      if (allErrors.length > 0) {
        console.log(
          `Test ${testNumber}: Alle Fehlermeldungen: ${allErrors.join(", ")}`
        );
        throw new Error(`Login-Validierungsfehler: ${allErrors.join(", ")}`);
      }
    }

    try {
      await page.waitForURL("**/orders", { timeout: 5000 });
      console.log(
        `Test ${testNumber}: Automatische Weiterleitung zu /orders erkannt`
      );

      await page.evaluate(() => {
        localStorage.setItem(
          "tour-store",
          '{"state":{"showTour":false},"version":0}'
        );
      });
    } catch (urlError) {
      console.log(
        `Test ${testNumber}: Keine automatische Weiterleitung zu /orders - prüfe aktuelle URL`
      );
      await page.waitForLoadState("networkidle", { timeout: 5000 });
      const currentUrl = page.url();
      console.log(`Test ${testNumber}: Aktuelle URL nach Login: ${currentUrl}`);

      if (currentUrl.includes("/login")) {
        const pageContent = await page.content();
        if (
          pageContent.includes("Benutzername") &&
          pageContent.includes("Passwort")
        ) {
          const finalErrorCheck = await page
            .locator("div.text-red-500")
            .allTextContents();
          if (finalErrorCheck.length > 0) {
            throw new Error(
              `Login fehlgeschlagen mit Fehlermeldungen: ${finalErrorCheck.join(
                ", "
              )}`
            );
          } else {
            throw new Error(
              "Login fehlgeschlagen - noch auf Login-Seite, möglicherweise falsche Anmeldedaten"
            );
          }
        }
      } else {
        await page.evaluate(() => {
          localStorage.setItem(
            "tour-store",
            '{"state":{"showTour":false},"version":0}'
          );
        });
      }
    }

    const loginTime = Date.now() - loginStart;

    const ordersStart = Date.now();
    const currentUrl = page.url();

    if (!currentUrl.includes("/orders")) {
      console.log(`Test ${testNumber}: Navigiere manuell zu /orders`);
      await page.goto("https://slwebtest.storelogix.de/orders");
      await page.waitForLoadState("networkidle", { timeout: 5000 });
    } else {
      await page.waitForLoadState("networkidle", { timeout: 5000 });
    }

    const ordersTime = Date.now() - ordersStart;

    await page.waitForTimeout(2000);

    const stocksStart = Date.now();
    const response = await page.goto("https://slwebtest.storelogix.de/stock");

    if (response.status() !== 200) {
      throw new Error(`Stocks-Seite Statuscode: ${response.status()}`);
    }

    await page.waitForLoadState("networkidle", { timeout: 5000 });
    const stocksTime = Date.now() - stocksStart;
    const totalTime = Date.now() - startTime;

    metrics.addResult(
      testNumber,
      loginTime,
      ordersTime,
      stocksTime,
      totalTime,
      errors
    );

    console.log(`Test ${testNumber}: Erfolgreich (${totalTime}ms)`);
  } catch (error) {
    const totalTime = Date.now() - startTime;
    errors.push(error.message);
    metrics.addResult(testNumber, 0, 0, 0, totalTime, errors);
    console.log(`Test ${testNumber}: Fehler - ${error.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function runLoadTest() {
  const concurrency = 10;
  const metrics = new PerformanceMetrics();

  console.log(`Starte Load Test mit ${concurrency} parallelen Tests...`);

  const testPromises = [];
  for (let i = 1; i <= concurrency; i++) {
    testPromises.push(runSingleTest(i, metrics));
  }

  await Promise.all(testPromises);

  metrics.printReport();
}

runLoadTest().catch(console.error);
