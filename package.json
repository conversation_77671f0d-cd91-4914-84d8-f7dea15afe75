{"name": "lasttest-alternativen", "version": "1.0.0", "description": "Verschiedene Ansätze für Lasttests", "main": "lasttest.js", "type": "module", "scripts": {"test:browser": "node lasttest.js", "test:browser-headed": "node headed-browser-test.js", "test:http": "node http-loadtest.js", "test:artillery": "artillery run artillery-test.yml", "test:k6": "k6 run k6-test.js", "install-tools": "npm install axios && npm install -g artillery k6"}, "dependencies": {"@playwright/test": "^1.40.0", "axios": "^1.10.0"}, "keywords": ["loadtest", "performance", "testing"], "author": "", "license": "ISC"}